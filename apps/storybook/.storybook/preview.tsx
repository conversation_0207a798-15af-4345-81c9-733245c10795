import React from 'react';

import type { Preview } from '@storybook/nextjs';

import { cn } from '@kit/ui/utils';

import { heading, sans } from '~/lib/fonts';
import '~/styles/globals.css';

const font = [sans.variable, heading.variable].reduce<string[]>((acc, curr) => {
  if (acc.includes(curr)) return acc;

  return [...acc, curr];
}, []);

const preview: Preview = {
  parameters: {
    nextjs: {
      appDirectory: true,
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#F8F7F4' },
        { name: 'dark', value: '#09090B' },
      ],
    },
    layout: 'centered',
  },

  decorators: [
    (Story) => (
      <div className={cn('font-sans antialiased', ...font)}>
        <Story />
      </div>
    ),
  ],

  tags: ['autodocs']
};

export default preview;
