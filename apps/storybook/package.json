{"name": "storybooks", "version": "0.1.0", "private": true, "scripts": {"storybook": "storybook dev -p 6006 --no-open", "build": "storybook build", "lint": "eslint . --ext .ts,.tsx", "test": "npm run test:storybook && npm run test:performance", "test:unit": "jest", "test:storybook": "test-storybook --url http://localhost:6006", "test:performance": "test-storybook --url http://localhost:6006 --test-runner=@storybook/test-runner/playwright/test-runner --stories-json", "chromatic": "chromatic --exit-zero-on-changes", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf storybook-static"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@kit/i18n": "workspace:*", "@kit/shared": "workspace:*", "@kit/ui": "workspace:*", "@tanstack/react-query": "5.80.6", "@tiptap/react": "^2.14.0", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.57.0", "recharts": "2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "zod": "^3.25.56"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0-next.16", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@playwright/test": "^1.52.0", "@storybook/addon-a11y": "^9.1.0-alpha.3", "@storybook/addon-coverage": "^1.0.5", "@storybook/addon-docs": "^9.1.0-alpha.3", "@storybook/addon-onboarding": "^9.1.0-alpha.3", "@storybook/addon-themes": "^9.1.0-alpha.3", "@storybook/nextjs": "^9.1.0-alpha.3", "@storybook/test-runner": "^0.23.0-next.4", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "axe-playwright": "^2.1.0", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-plugin-storybook": "^9.1.0-alpha.3", "postcss": "8.5.4", "storybook": "^9.1.0-alpha.3", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}