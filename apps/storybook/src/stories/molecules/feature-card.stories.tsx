'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Code2, La<PERSON>op, Rocket, Zap } from 'lucide-react';

import { FeatureCard } from '@kit/ui/dojo/molecules/feature-card';

const meta = {
  title: 'Molecules/Marketing/FeatureCard',
  component: FeatureCard,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    label: {
      control: 'text',
      description: 'The title of the feature',
    },
    description: {
      control: 'text',
      description: 'Description of the feature',
    },
    image: {
      description: 'Optional image or icon component',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof FeatureCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Lightning Fast',
    description: 'Built for speed and performance, delivering instant results.',
    image: (
      <div className="flex justify-center">
        <Zap className="text-primary h-12 w-12" />
      </div>
    ),
  },
};

export const WithCustomStyle: Story = {
  args: {
    label: 'Modern Development',
    description: 'Using the latest technologies and best practices.',
    image: (
      <div className="flex justify-center">
        <Code2 className="text-primary h-12 w-12" />
      </div>
    ),
    className: 'bg-primary/5',
  },
};

export const WithLongDescription: Story = {
  args: {
    label: 'Responsive Design',
    description:
      'Fully responsive and adaptive to all screen sizes, ensuring a great user experience across all devices.',
    image: (
      <div className="flex justify-center">
        <Laptop className="text-primary h-12 w-12" />
      </div>
    ),
  },
};

export const WithCustomContent: Story = {
  args: {
    label: 'Advanced Features',
    description: 'Packed with powerful features for developers.',
    image: (
      <div className="flex justify-center">
        <Rocket className="text-primary h-12 w-12" />
      </div>
    ),
    children: (
      <div className="mt-4 space-y-2">
        <div className="text-muted-foreground text-sm">Key features:</div>
        <ul className="text-muted-foreground list-inside list-disc text-sm">
          <li>TypeScript support</li>
          <li>Modern build tools</li>
          <li>Performance optimized</li>
        </ul>
      </div>
    ),
  },
};

export const Grid: Story = {
  args: {
    label: '',
    description: '',
  },
  render: () => (
    <div className="grid grid-cols-2 gap-4">
      <FeatureCard
        label="Lightning Fast"
        description="Built for speed and performance."
        image={
          <div className="flex justify-center">
            <Zap className="text-primary h-12 w-12" />
          </div>
        }
      />
      <FeatureCard
        label="Modern Development"
        description="Latest technologies and practices."
        image={
          <div className="flex justify-center">
            <Code2 className="text-primary h-12 w-12" />
          </div>
        }
      />
      <FeatureCard
        label="Responsive Design"
        description="Adaptive to all screen sizes."
        image={
          <div className="flex justify-center">
            <Laptop className="text-primary h-12 w-12" />
          </div>
        }
      />
      <FeatureCard
        label="Advanced Features"
        description="Powerful developer tools."
        image={
          <div className="flex justify-center">
            <Rocket className="text-primary h-12 w-12" />
          </div>
        }
      />
    </div>
  ),
};
