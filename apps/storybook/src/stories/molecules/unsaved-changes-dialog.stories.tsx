import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import { UnsavedChangesDialog } from '@kit/ui/dojo/molecules/unsaved-changes-dialog';
import { Input } from '@kit/ui/input';

// Demo component to show realistic usage
function DemoEditor({
  title = 'Unsaved Changes',
  description = 'You have unsaved changes. Do you want to proceed without saving?',
  stayButtonText = 'Stay Here',
  proceedButtonText = 'Proceed Without Saving',
  'data-test': dataTest,
}: Partial<typeof UnsavedChangesDialog.arguments>) {
  const [text, setText] = useState('');
  const [showDialog, setShowDialog] = useState(false);

  const handleSubmit = () => {
    if (text.trim()) {
      setShowDialog(true);
    }
  };

  return (
    <div className="w-[400px] space-y-4 rounded-lg border p-4">
      <div className="space-y-2">
        <Input
          placeholder="Type something..."
          value={text}
          onChange={(e) => setText(e.target.value)}
          data-test={`${dataTest}-input`}
        />
        <Button onClick={handleSubmit} data-test={`${dataTest}-submit`}>
          Submit
        </Button>
      </div>

      <UnsavedChangesDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onProceed={() => {
          setText('');
          setShowDialog(false);
        }}
        onStay={() => setShowDialog(false)}
        title={title}
        description={description}
        stayButtonText={stayButtonText}
        proceedButtonText={proceedButtonText}
        data-test={dataTest}
      />
    </div>
  );
}

const meta = {
  title: 'Molecules/UnsavedChangesDialog',
  component: DemoEditor,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof DemoEditor>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const CustomText: Story = {
  args: {
    title: 'Save Your Progress?',
    description: 'Your changes will be lost if you leave without saving.',
    stayButtonText: 'Continue Editing',
    proceedButtonText: 'Discard Changes',
  },
};

export const LongText: Story = {
  args: {
    title: 'Important Notice About Your Unsaved Changes',
    description:
      'You have made several important modifications to this document that have not been saved. If you proceed without saving, all these changes will be permanently lost. Are you sure you want to continue?',
    stayButtonText: 'Return to Editor',
    proceedButtonText: 'Yes, Discard All Changes',
  },
};

export const WithTestAttributes: Story = {
  args: {
    'data-test': 'unsaved-changes-dialog',
  },
};
