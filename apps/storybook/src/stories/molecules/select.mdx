import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as SelectStories from './select.stories';

<Meta of={SelectStories} />

# Select

A dropdown select component with support for groups, icons, and disabled states.

## Features

- Single selection from a list of options
- Option grouping with labels
- Custom trigger with icon support
- Disabled states (both select and individual items)
- Keyboard navigation
- Screen reader support
- Customizable styling
- Scrollable content with up/down buttons

## Usage

```tsx
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';

export default function MySelect() {
  return (
    <Select>
      <SelectTrigger>
        <SelectValue placeholder="Select an option" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Options</SelectLabel>
          <SelectItem value="option1">Option 1</SelectItem>
          <SelectItem value="option2">Option 2</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
```

## Examples

### Default

A basic select component with a single group of options.

<Canvas of={SelectStories.Default} />

### With Groups

Multiple option groups with a separator.

<Canvas of={SelectStories.WithGroups} />

### With Icon

Select with an icon in the trigger.

<Canvas of={SelectStories.WithIcon} />

### Disabled

A disabled select component.

<Canvas of={SelectStories.Disabled} />

### With Disabled Items

Select with some disabled options.

<Canvas of={SelectStories.WithDisabledItems} />

## Component API

### Select

The root select component.

#### Props

- `defaultValue?`: string - The default selected value
- `value?`: string - The controlled value
- `onValueChange?`: (value: string) => void - Called when value changes
- `disabled?`: boolean - Whether the select is disabled
- `required?`: boolean - Whether the select is required
- `name?`: string - The name of the select when used in a form

### SelectTrigger

The button that opens the select.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - Usually contains SelectValue
- `disabled?`: boolean - Whether the trigger is disabled

### SelectValue

The component that displays the selected value.

#### Props

- `placeholder?`: string - Text to show when no value is selected
- `children?`: React.ReactNode - Custom content for the value

### SelectContent

The popover content that contains the select options.

#### Props

- `position?`: "item" | "popper" - The positioning strategy
- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - SelectGroup and SelectItem components

### SelectGroup

A group of select options with an optional label.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - SelectLabel and SelectItem components

### SelectItem

An individual select option.

#### Props

- `value`: string - The value of the item
- `disabled?`: boolean - Whether the item is disabled
- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - The content of the item

### SelectLabel

A label for a group of options.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - The label text

### SelectSeparator

A visual separator between groups.

#### Props

- `className?`: string - Additional CSS classes

## Accessibility

The Select component follows WAI-ARIA guidelines:

- Uses `role="combobox"` for the trigger
- Proper ARIA labels and descriptions
- Keyboard navigation support
- Focus management
- Screen reader announcements

### Keyboard Interactions

- `Space/Enter`: Open/close the select
- `Arrow Up/Down`: Navigate through options
- `Home/End`: Jump to first/last option
- `Escape`: Close the select
- `Tab`: Move focus to next/previous element

## Guidelines

### Usage Guidelines

1. **Selection Design**

   - Use clear, concise option labels
   - Group related options together
   - Consider using icons for visual clarity
   - Limit the number of options for better usability

2. **Layout Considerations**

   - Ensure sufficient width for option text
   - Consider mobile touch targets
   - Use appropriate spacing between options
   - Position the select logically in forms

3. **Interaction States**
   - Provide clear visual feedback
   - Handle loading states appropriately
   - Consider empty/error states
   - Use disabled states judiciously

### Content Guidelines

1. **Option Labels**

   - Use clear, descriptive text
   - Keep labels concise
   - Use sentence case
   - Consider internationalization

2. **Placeholders**
   - Use action-oriented text
   - Avoid using "Select..."
   - Be specific about expected input
   - Consider empty state messaging

### Error Handling

1. **Validation**

   - Handle required field validation
   - Show clear error messages
   - Provide guidance for correction
   - Consider form context

2. **Edge Cases**
   - Handle long option text
   - Consider no options available
   - Handle network errors
   - Manage state transitions

## Performance Considerations

1. **Optimization**

   - Lazy load options if needed
   - Virtualize long lists
   - Minimize option re-renders
   - Cache selected values

2. **Loading States**
   - Show loading indicators
   - Handle async options
   - Maintain UI responsiveness
   - Consider skeleton states

## Responsive Design

1. **Mobile Considerations**

   - Ensure touch-friendly targets
   - Consider native select fallback
   - Handle different screen sizes
   - Test on various devices

2. **Viewport Adaptations**
   - Adjust positioning
   - Handle orientation changes
   - Consider content overflow
   - Maintain usability across devices

<Controls />
