'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { useForm } from 'react-hook-form';

import { CommunityCategorySelector } from '@kit/ui/dojo/molecules/community-category-selector';
import { Form } from '@kit/ui/form';

const meta = {
  title: 'Molecules/CommunityCategorySelector',
  component: CommunityCategorySelector,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => {
      const form = useForm({
        defaultValues: {
          category: '',
        },
      });
      return (
        <Form {...form}>
          <form className="w-[300px]">
            <Story />
          </form>
        </Form>
      );
    },
  ],
} satisfies Meta<typeof CommunityCategorySelector>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockCategories = [
  {
    id: '1',
    name: 'education',
    icon: '🎓',
  },
  {
    id: '2',
    name: 'technology',
    icon: '💻',
  },
  {
    id: '3',
    name: 'business',
    icon: '💼',
  },
  {
    id: '4',
    name: 'arts',
    icon: '🎨',
  },
];

export const Default: Story = {
  args: {
    currentCategoryId: '1',
    categories: mockCategories,
    onChange: (value) => console.log('Selected category:', value),
  },
};

export const WithPreselectedCategory: Story = {
  args: {
    currentCategoryId: '2',
    categories: mockCategories,
    onChange: (value) => console.log('Selected category:', value),
  },
};

export const WithCustomCategories: Story = {
  args: {
    currentCategoryId: 'custom1',
    categories: [
      {
        id: 'custom1',
        name: 'gaming',
        icon: '🎮',
      },
      {
        id: 'custom2',
        name: 'fitness',
        icon: '💪',
      },
      {
        id: 'custom3',
        name: 'cooking',
        icon: '👨‍🍳',
      },
    ],
    onChange: (value) => console.log('Selected category:', value),
  },
};

export const EmptyCategories: Story = {
  args: {
    currentCategoryId: '',
    categories: [],
    onChange: (value) => console.log('Selected category:', value),
  },
};
