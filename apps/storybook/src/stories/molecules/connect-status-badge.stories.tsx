import type { Meta, StoryObj } from '@storybook/nextjs';

import { ConnectStatusBadge } from '@kit/ui/dojo/molecules/connect-status-badge';

const meta: Meta<typeof ConnectStatusBadge> = {
  title: 'Molecules/ConnectStatusBadge',
  component: ConnectStatusBadge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ConnectStatusBadge>;

export const Active: Story = {
  args: {
    status: {
      detailsSubmitted: true,
      chargesEnabled: true,
      payoutsEnabled: true,
    },
  },
};

export const Pending: Story = {
  args: {
    status: {
      detailsSubmitted: true,
      chargesEnabled: false,
      payoutsEnabled: false,
    },
  },
};

export const Incomplete: Story = {
  args: {
    status: {
      detailsSubmitted: false,
      chargesEnabled: false,
      payoutsEnabled: false,
    },
  },
};

export const Partial: Story = {
  args: {
    status: {
      detailsSubmitted: true,
      chargesEnabled: true,
      payoutsEnabled: false,
    },
  },
};
