'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { ModeToggle, SubMenuModeToggle } from '@kit/ui/mode-toggle';

const meta = {
  title: 'Molecules/ModeToggle',
  component: ModeToggle,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    themes: {
      default: 'light',
      list: [
        { name: 'light', class: 'light', color: '#ffffff' },
        { name: 'dark', class: 'dark', color: '#000000' },
      ],
    },
  },
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof ModeToggle>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: (args) => <ModeToggle {...args} />,
};

export const WithCustomClass: Story = {
  render: (args) => (
    <ModeToggle {...args} className="rounded-full border-2 p-2" />
  ),
};

export const InDropdownMenu: Story = {
  render: () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">Open Menu</Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <SubMenuModeToggle />
      </DropdownMenuContent>
    </DropdownMenu>
  ),
};

export const ResponsiveLayout: Story = {
  parameters: {
    layout: 'padded',
  },
  render: () => (
    <div className="w-full max-w-sm space-y-4">
      <div className="flex items-center justify-between rounded-lg border p-4">
        <span className="text-sm font-medium">Toggle Theme</span>
        <ModeToggle />
      </div>
      <div className="rounded-lg border">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start p-4">
              <span className="text-sm font-medium">Settings</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-full">
            <SubMenuModeToggle />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  ),
};
