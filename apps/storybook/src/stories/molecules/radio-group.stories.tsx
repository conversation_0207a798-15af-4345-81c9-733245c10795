import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import {
  RadioGroup,
  RadioGroupItem,
  RadioGroupItemLabel,
} from '@kit/ui/radio-group';

const meta = {
  title: 'Molecules/RadioGroup',
  component: RadioGroup,
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'text',
      description: 'The default selected value',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the radio group is disabled',
    },
    required: {
      control: 'boolean',
      description: 'Whether the radio group is required',
    },
    name: {
      control: 'text',
      description: 'The name of the radio group',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof RadioGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <RadioGroup defaultValue="option1">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option1" id="option1" />
        <label htmlFor="option1">Option 1</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option2" id="option2" />
        <label htmlFor="option2">Option 2</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option3" id="option3" />
        <label htmlFor="option3">Option 3</label>
      </div>
    </RadioGroup>
  ),
};

export const WithLabels: Story = {
  render: () => (
    <RadioGroup defaultValue="card1" className="grid gap-4">
      <RadioGroupItemLabel selected>
        <RadioGroupItem value="card1" id="card1" />
        <div>
          <label htmlFor="card1" className="font-medium">
            Standard
          </label>
          <p className="text-muted-foreground text-sm">
            Free plan for personal use
          </p>
        </div>
      </RadioGroupItemLabel>
      <RadioGroupItemLabel>
        <RadioGroupItem value="card2" id="card2" />
        <div>
          <label htmlFor="card2" className="font-medium">
            Pro
          </label>
          <p className="text-muted-foreground text-sm">
            Perfect for professionals
          </p>
        </div>
      </RadioGroupItemLabel>
    </RadioGroup>
  ),
};

export const Disabled: Story = {
  render: () => (
    <RadioGroup disabled defaultValue="option1">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option1" id="disabled1" />
        <label htmlFor="disabled1" className="text-muted-foreground">
          Disabled Option 1
        </label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option2" id="disabled2" />
        <label htmlFor="disabled2" className="text-muted-foreground">
          Disabled Option 2
        </label>
      </div>
    </RadioGroup>
  ),
};

export const Required: Story = {
  render: () => (
    <RadioGroup required name="required-options" defaultValue="option1">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option1" id="required1" />
        <label htmlFor="required1">Required Option 1</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option2" id="required2" />
        <label htmlFor="required2">Required Option 2</label>
      </div>
    </RadioGroup>
  ),
};

export const Horizontal: Story = {
  render: () => (
    <RadioGroup defaultValue="option1" className="flex space-x-4">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option1" id="horizontal1" />
        <label htmlFor="horizontal1">Option 1</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option2" id="horizontal2" />
        <label htmlFor="horizontal2">Option 2</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option3" id="horizontal3" />
        <label htmlFor="horizontal3">Option 3</label>
      </div>
    </RadioGroup>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <RadioGroup defaultValue="option1" className="grid gap-4">
      <RadioGroupItemLabel selected className="bg-primary/5">
        <RadioGroupItem value="option1" id="custom1" />
        <div>
          <label htmlFor="custom1" className="text-primary font-semibold">
            Premium
          </label>
          <p className="text-muted-foreground text-sm">
            Enhanced features and support
          </p>
        </div>
      </RadioGroupItemLabel>
      <RadioGroupItemLabel className="border-dashed">
        <RadioGroupItem value="option2" id="custom2" />
        <div>
          <label htmlFor="custom2" className="font-semibold">
            Enterprise
          </label>
          <p className="text-muted-foreground text-sm">
            Custom solutions for large teams
          </p>
        </div>
      </RadioGroupItemLabel>
    </RadioGroup>
  ),
};
