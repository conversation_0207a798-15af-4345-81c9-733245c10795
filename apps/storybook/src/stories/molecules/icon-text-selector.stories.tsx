'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { useForm } from 'react-hook-form';

import { IconTextSelector } from '@kit/ui/dojo/molecules/icon-text-selector';
import { Form } from '@kit/ui/form';

const meta = {
  title: 'Molecules/IconTextSelector',
  component: IconTextSelector,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => {
      const form = useForm({
        defaultValues: {
          item: '',
        },
      });
      return (
        <Form {...form}>
          <form className="w-[300px]">
            <Story />
          </form>
        </Form>
      );
    },
  ],
} satisfies Meta<typeof IconTextSelector>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockItems = [
  {
    id: '1',
    name: 'English',
    icon: '🇺🇸',
  },
  {
    id: '2',
    name: 'Spanish',
    icon: '🇪🇸',
  },
  {
    id: '3',
    name: 'French',
    icon: '🇫🇷',
  },
  {
    id: '4',
    name: 'German',
    icon: '🇩🇪',
  },
];

export const Default: Story = {
  args: {
    placeholder: 'Select a language',
    description: 'Choose your preferred language',
    currentId: '1',
    items: mockItems,
    onChange: (value) => console.log('Selected item:', value),
  },
};

export const WithPreselectedItem: Story = {
  args: {
    placeholder: 'Select a language',
    description: 'Choose your preferred language',
    currentId: '2',
    items: mockItems,
    onChange: (value) => console.log('Selected item:', value),
  },
};

export const WithCustomItems: Story = {
  args: {
    placeholder: 'Select a country',
    description: 'Choose your country',
    currentId: 'custom1',
    items: [
      {
        id: 'custom1',
        name: 'Japan',
        icon: '🇯🇵',
      },
      {
        id: 'custom2',
        name: 'Korea',
        icon: '🇰🇷',
      },
      {
        id: 'custom3',
        name: 'China',
        icon: '🇨🇳',
      },
    ],
    onChange: (value) => console.log('Selected item:', value),
  },
};

export const EmptyItems: Story = {
  args: {
    placeholder: 'No languages available',
    description: 'Choose your preferred language',
    currentId: '',
    items: [],
    onChange: (value) => console.log('Selected item:', value),
  },
};
