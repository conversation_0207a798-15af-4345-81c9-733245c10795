import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { SocialLinks } from '@kit/ui/dojo/molecules/social-links';

const meta = {
  title: 'Molecules/SocialLinks',
  component: SocialLinks,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof SocialLinks>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default SocialLinks component with various social platforms
 */
export const Default: Story = {
  args: {
    socials: [
      { name: 'github', url: 'https://github.com/username' },
      { name: 'twitter', url: 'https://twitter.com/username' },
      { name: 'linkedin', url: 'https://linkedin.com/in/username' },
      { name: 'instagram', url: 'https://instagram.com/username' },
      { name: 'facebook', url: 'https://facebook.com/username' },
    ],
  },
};

/**
 * SocialLinks with limited platforms
 */
export const Limited: Story = {
  args: {
    socials: [
      { name: 'github', url: 'https://github.com/username' },
      { name: 'linkedin', url: 'https://linkedin.com/in/username' },
    ],
  },
};

/**
 * SocialLinks with all supported platforms
 */
export const AllPlatforms: Story = {
  args: {
    socials: [
      { name: 'github', url: 'https://github.com/username' },
      { name: 'twitter', url: 'https://twitter.com/username' },
      { name: 'linkedin', url: 'https://linkedin.com/in/username' },
      { name: 'instagram', url: 'https://instagram.com/username' },
      { name: 'facebook', url: 'https://facebook.com/username' },
      { name: 'youtube', url: 'https://youtube.com/c/username' },
      { name: 'tiktok', url: 'https://tiktok.com/@username' },
      { name: 'website', url: 'https://example.com' },
    ],
  },
};

/**
 * SocialLinks with custom styling
 */
export const CustomStyling: Story = {
  args: {
    socials: [
      { name: 'github', url: 'https://github.com/username' },
      { name: 'twitter', url: 'https://twitter.com/username' },
      { name: 'linkedin', url: 'https://linkedin.com/in/username' },
    ],
    className: 'gap-5',
  },
};
