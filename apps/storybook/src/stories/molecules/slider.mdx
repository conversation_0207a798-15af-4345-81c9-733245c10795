import { Canvas, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as SliderStories from './slider.stories';

<Meta of={SliderStories} />

# Slider

A slider component that allows users to make selections from a range of values.

## Features

- Single value selection
- Range selection with two handles
- Customizable step values
- Keyboard navigation
- Screen reader support
- Disabled state
- Custom styling options
- Touch device support

## Usage

```tsx
import { Slider } from '@kit/ui/slider';

export default function MySlider() {
  return (
    <Slider
      defaultValue={[50]}
      min={0}
      max={100}
      step={1}
      onValueChange={(value) => console.log(value)}
    />
  );
}
```

## Examples

### Default

A basic slider with a single handle.

<Canvas of={SliderStories.Default} />

### Range Slider

A slider with two handles for selecting a range of values.

<Canvas of={SliderStories.RangeSlider} />

### Stepped Slider

A slider with larger step increments for more precise selection.

<Canvas of={SliderStories.SteppedSlider} />

### Disabled Slider

A slider in a disabled state.

<Canvas of={SliderStories.DisabledSlider} />

### With Labels

A slider with labels showing the current value.

<Canvas of={SliderStories.WithLabels} />

## Component API

### Slider

The root slider component.

#### Props

- `defaultValue`: number[] - The default value(s) of the slider
- `value?`: number[] - Controlled value(s) of the slider
- `onValueChange?`: (value: number[]) => void - Called when the value changes
- `onValueCommit?`: (value: number[]) => void - Called when the value change is committed
- `min`: number - The minimum value
- `max`: number - The maximum value
- `step`: number - The step increment
- `disabled?`: boolean - Whether the slider is disabled
- `orientation?`: "horizontal" | "vertical" - The orientation of the slider
- `dir?`: "ltr" | "rtl" - The reading direction
- `inverted?`: boolean - Whether the scale is inverted
- `className?`: string - Additional CSS classes

## Accessibility

The Slider component follows WAI-ARIA guidelines:

- Uses `role="slider"` for the thumb
- Provides proper ARIA attributes
- Supports keyboard navigation
- Screen reader announcements
- Focus management

### Keyboard Interactions

- `ArrowRight/ArrowUp`: Increase value
- `ArrowLeft/ArrowDown`: Decrease value
- `Home`: Set to minimum value
- `End`: Set to maximum value
- `PageUp`: Increase value by larger step
- `PageDown`: Decrease value by larger step

## Guidelines

### Usage Guidelines

1. **Value Selection**

   - Use appropriate step sizes
   - Consider precision needs
   - Provide visual feedback
   - Show current value

2. **Layout Considerations**

   - Use consistent widths
   - Consider touch targets
   - Maintain visual hierarchy
   - Account for labels

3. **Interaction Design**
   - Smooth value updates
   - Clear visual feedback
   - Handle touch events
   - Consider mobile use

### Content Guidelines

1. **Labels and Values**

   - Use clear labels
   - Show units if needed
   - Format numbers appropriately
   - Consider i18n

2. **Visual Design**
   - Clear track visibility
   - Distinct thumb design
   - Appropriate contrast
   - Consistent styling

### Error Handling

1. **Value Validation**

   - Handle out-of-range values
   - Validate min/max
   - Check step alignment
   - Handle edge cases

2. **State Management**
   - Track value changes
   - Handle controlled state
   - Manage focus state
   - Handle disabled state

## Performance Considerations

1. **Event Handling**

   - Debounce value updates
   - Optimize change events
   - Manage re-renders
   - Handle touch events

2. **Rendering**
   - Minimize DOM updates
   - Use CSS transforms
   - Handle window resize
   - Consider SSR

## Responsive Design

1. **Mobile Considerations**

   - Touch-friendly targets
   - Handle gestures
   - Consider thumb size
   - Test on devices

2. **Viewport Adaptations**
   - Adjust size responsively
   - Handle orientation
   - Consider breakpoints
   - Test different scales

<Controls />{' '}
