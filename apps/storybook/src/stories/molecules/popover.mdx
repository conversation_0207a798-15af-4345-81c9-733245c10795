import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as PopoverStories from './popover.stories';

<Meta of={PopoverStories} />

# Popover

A Popover component that opens a floating content panel when its trigger is clicked. Built on top of Radix UI's Popover primitive.

## Features

- Controlled and uncontrolled modes
- Customizable positioning (top, right, bottom, left)
- Keyboard navigation and focus management
- Screen reader announcements
- Animation support
- Responsive design

## Usage

```tsx
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';

export function MyComponent() {
  return (
    <Popover>
      <PopoverTrigger>Open</PopoverTrigger>
      <PopoverContent>
        <div>Popover content</div>
      </PopoverContent>
    </Popover>
  );
}
```

## Examples

### Default

A basic popover with a button trigger and simple content.

<Canvas of={PopoverStories.Default} />

### With Icon

A popover triggered by an icon button.

<Canvas of={PopoverStories.WithIcon} />

### With Form

A popover containing a form for editing profile information.

<Canvas of={PopoverStories.WithForm} />

### Custom Positioning

Examples of popovers with different positions (top, right, bottom, left).

<Canvas of={PopoverStories.WithCustomPosition} />

## Component API

<Controls />

## Subcomponents

### Popover

The root component that manages the state of the popover.

Props:

- `defaultOpen?: boolean` - Initial open state
- `open?: boolean` - Controlled open state
- `onOpenChange?: (open: boolean) => void` - Open state change handler
- `modal?: boolean` - Whether to render as a modal

### PopoverTrigger

The button that triggers the popover.

Props:

- `asChild?: boolean` - When true, the trigger will be replaced by its child component
- All button HTML attributes

### PopoverContent

The content of the popover. Renders in a portal at the root of the document.

Props:

- `className?: string` - Additional CSS classes
- `align?: 'start' | 'center' | 'end'` - Alignment against the trigger
- `sideOffset?: number` - Distance in pixels from the trigger
- `side?: 'top' | 'right' | 'bottom' | 'left'` - Preferred side of the trigger
- `alignOffset?: number` - Offset in pixels from the alignment edge
- `avoidCollisions?: boolean` - Whether to flip the content when it would collide with the viewport
- `collisionBoundary?: Element | null` - Element to check for collisions against
- `collisionPadding?: number | object` - Padding between content and viewport edges

## Guidelines

### Usage Guidelines

- Use popovers for non-critical information or actions
- Keep content concise and focused
- Ensure the trigger clearly indicates a popover will appear
- Consider mobile users and touch interactions

### Content Guidelines

- Use clear, concise headings
- Group related information
- Include a clear call-to-action if needed
- Avoid long scrolling content

### Accessibility

- Follows WAI-ARIA Popover Pattern
- Keyboard navigation: Tab, Space/Enter, Escape
- Screen reader announcements
- Focus management
- ARIA attributes for state and relationships

### Performance

- Lazy loading of content
- Portal rendering for optimal stacking
- Animation optimizations
- Efficient event handling

### Error Handling

- Graceful fallback for unsupported browsers
- Error boundaries for content rendering
- Validation for form inputs
- Clear error messages and states
