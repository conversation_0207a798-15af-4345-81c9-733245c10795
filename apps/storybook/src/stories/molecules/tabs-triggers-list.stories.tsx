'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Bell, Settings2, User } from 'lucide-react';

import { Tabs, TabsContent, TabsList } from '@kit/ui/tabs';
import { TabsTriggersList } from '@kit/ui/tabs-triggers-list';

const meta = {
  title: 'Molecules/TabsTriggersList',
  component: TabsTriggersList,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    values: {
      control: { type: 'object' },
      description: 'Array of tab values',
    },
    labels: {
      control: { type: 'object' },
      description: 'Array of tab labels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof TabsTriggersList>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Horizontal: Story = {
  args: {
    values: ['account', 'password', 'settings'],
    labels: ['Settings', 'Password', 'General Settings'],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground px-4 py-2 font-medium',
  },
  render: (args) => (
    <Tabs defaultValue="account" className="w-[600px]">
      <TabsList className="bg-muted w-full justify-start rounded-lg p-1">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="mt-4 rounded-lg border p-4">
        <TabsContent value="account">Account settings content</TabsContent>
        <TabsContent value="password">Password settings content</TabsContent>
        <TabsContent value="settings">General settings content</TabsContent>
      </div>
    </Tabs>
  ),
};

export const HorizontalWithIcons: Story = {
  args: {
    values: ['profile', 'notifications', 'settings'],
    labels: [
      <div key="profile" className="flex items-center gap-2">
        <User className="h-4 w-4" /> Profile
      </div>,
      <div key="notifications" className="flex items-center gap-2">
        <Bell className="h-4 w-4" /> Notifications
      </div>,
      <div key="settings" className="flex items-center gap-2">
        <Settings2 className="h-4 w-4" /> Settings
      </div>,
    ],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground px-4 py-2 font-medium',
  },
  render: (args) => (
    <Tabs defaultValue="profile" className="w-[600px]">
      <TabsList className="bg-muted w-full justify-start rounded-lg p-1">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="mt-4 rounded-lg border p-4">
        <TabsContent value="profile">Profile settings content</TabsContent>
        <TabsContent value="notifications">
          Notifications settings content
        </TabsContent>
        <TabsContent value="settings">Settings content</TabsContent>
      </div>
    </Tabs>
  ),
};

export const Vertical: Story = {
  args: {
    values: ['overview', 'activity', 'settings'],
    labels: ['Overview', 'Activity', 'Settings'],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground h-9 w-full justify-start rounded-lg px-4 text-left hover:bg-muted',
  },
  render: (args) => (
    <Tabs
      defaultValue="overview"
      className="flex h-[400px]"
      orientation="vertical"
    >
      <TabsList className="mr-4 flex h-full w-[200px] flex-col justify-start space-y-2 rounded-lg border p-2">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="flex-1 rounded-lg border p-4">
        <TabsContent value="overview" className="h-full">
          Overview content
        </TabsContent>
        <TabsContent value="activity" className="h-full">
          Activity content
        </TabsContent>
        <TabsContent value="settings" className="h-full">
          Settings content
        </TabsContent>
      </div>
    </Tabs>
  ),
};

export const VerticalWithScroll: Story = {
  args: {
    values: ['tab1', 'tab2', 'tab3', 'tab4', 'tab5', 'tab6'],
    labels: [
      'First Tab',
      'Second Tab',
      'Third Tab',
      'Fourth Tab',
      'Fifth Tab',
      'Sixth Tab',
    ],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground h-9 w-full justify-start rounded-lg px-4 text-left hover:bg-muted',
  },
  render: (args) => (
    <Tabs defaultValue="tab1" className="flex h-[400px]" orientation="vertical">
      <TabsList className="mr-4 flex h-full w-[200px] flex-col justify-start space-y-2 overflow-y-auto rounded-lg border p-2">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="flex-1 rounded-lg border p-4">
        {args.values.map((tab) => (
          <TabsContent key={tab} value={tab} className="h-full">
            Content for {tab}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  ),
};

export const HorizontalWithScroll: Story = {
  args: {
    values: ['tab1', 'tab2', 'tab3', 'tab4', 'tab5', 'tab6'],
    labels: [
      'First Tab',
      'Second Tab',
      'Third Tab',
      'Fourth Tab',
      'Fifth Tab',
      'Sixth Tab',
    ],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground whitespace-nowrap px-4 py-2 font-medium',
  },
  render: (args) => (
    <Tabs defaultValue="tab1" className="w-[600px]">
      <TabsList className="bg-muted w-full justify-start overflow-x-auto rounded-lg p-1">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="mt-4 rounded-lg border p-4">
        {args.values.map((tab) => (
          <TabsContent key={tab} value={tab}>
            Content for {tab}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  ),
};

export const VerticalWithIcons: Story = {
  args: {
    values: ['profile', 'notifications', 'settings'],
    labels: [
      <div key="profile" className="flex items-center gap-2">
        <User className="h-4 w-4" /> Profile
      </div>,
      <div key="notifications" className="flex items-center gap-2">
        <Bell className="h-4 w-4" /> Notifications
      </div>,
      <div key="settings" className="flex items-center gap-2">
        <Settings2 className="h-4 w-4" /> Settings
      </div>,
    ],
    className:
      'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground h-9 w-full justify-start rounded-lg px-4 text-left hover:bg-muted',
  },
  render: (args) => (
    <Tabs
      defaultValue="profile"
      className="flex h-[400px]"
      orientation="vertical"
    >
      <TabsList className="mr-4 flex h-full w-[200px] flex-col justify-start space-y-2 rounded-lg border p-2">
        <TabsTriggersList {...args} />
      </TabsList>
      <div className="flex-1 rounded-lg border p-4">
        <TabsContent value="profile" className="h-full">
          Profile settings content
        </TabsContent>
        <TabsContent value="notifications" className="h-full">
          Notifications settings content
        </TabsContent>
        <TabsContent value="settings" className="h-full">
          Settings content
        </TabsContent>
      </div>
    </Tabs>
  ),
};
