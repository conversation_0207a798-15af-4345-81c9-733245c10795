import type { Meta, StoryObj } from '@storybook/nextjs';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Button } from '@kit/ui/button';
import { CommentHeader } from '@kit/ui/dojo/molecules/comment-header';

const meta = {
  title: 'Molecules/Comment Header',
  component: CommentHeader,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    avatarChildren: {
      control: 'object',
      description: 'ReactNode representing the avatar and author name.',
    },
    timestamp: { control: 'text' },
    timestampLabel: { control: 'text' },
    actions: { control: 'object' },
    className: { control: 'text' },
    ariaLabel: { control: 'text' },
  },
} satisfies Meta<typeof CommentHeader>;

export default meta;
type Story = StoryObj<typeof CommentHeader>;

// Helper function to create Avatar
const createAvatar = (args: {
  authorFirstName: string;
  authorLastName: string;
  authorPictureUrl?: string;
}) => (
  <div className="flex items-center gap-2">
    <Avatar>
      {args.authorPictureUrl && <AvatarImage src={args.authorPictureUrl} />}
      <AvatarFallback>
        {args.authorFirstName?.charAt(0)}
        {args.authorLastName?.charAt(0)}
      </AvatarFallback>
    </Avatar>
    <span className="font-semibold">
      {args.authorFirstName} {args.authorLastName}
    </span>
  </div>
);

export const Default: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300',
    }),
    timestamp: '2 hours ago',
  },
};

export const WithTimestampLabel: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Jane',
      authorLastName: 'Smith',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=5',
    }),
    timestamp: '2024-07-27 10:30:00',
    timestampLabel: 'Sent on',
  },
};

export const LongAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Dr. Alexandra Richardson-Montgomery III',
      authorLastName: 'Richardson-Montgomery',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=10',
    }),
    timestamp: '1 day ago',
  },
};

export const EmptyAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: '',
      authorLastName: '',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=15',
    }),
    timestamp: 'just now',
  },
};

export const RTLAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'محمد',
      authorLastName: 'أحمد',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=20',
    }),
    timestamp: 'منذ 5 دقائق',
  },
};

export const WithScreenReaderLabel: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=25',
    }),
    timestamp: '30 seconds ago',
    ariaLabel: "Comment header for John Doe's post",
  },
};

export const CustomClassName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=30',
    }),
    timestamp: 'yesterday',
    className: 'bg-blue-100 dark:bg-blue-900 p-4',
  },
};

export const WithActions: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Action',
      authorLastName: 'User',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=32',
    }),
    timestamp: '10 minutes ago',
    actions: (
      <>
        <Button variant="ghost" size="sm">
          Edit
        </Button>
        <Button variant="destructive" size="sm">
          Delete
        </Button>
      </>
    ),
  },
};

export const VeryLongTimestamp: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=35',
    }),
    timestamp:
      "This is an exceedingly long timestamp description that should ideally wrap or be truncated gracefully within the component's layout constraints to avoid breaking the UI.",
  },
};

export const HTMLInAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: '<script>alert("xss")</script>',
      authorLastName: '<b>Test</b>',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=40',
    }),
    timestamp: '5 minutes ago',
  },
};

export const EmojiAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: '🎋 Sensei',
      authorLastName: '🗡️',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=45',
    }),
    timestamp: '1 hour ago',
  },
};

export const SpecialCharacters: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: '→ ∑ß▲',
      authorLastName: '←',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=50',
    }),
    timestamp: '2 days ago',
  },
};

export const MultilineAuthorName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Line 1',
      authorLastName: 'Line 2',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=55',
    }),
    timestamp: '3 weeks ago',
  },
};

export const ZeroWidthCharacters: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John\\u200BDoe',
      authorLastName: 'Doe\\u200B',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=60',
    }),
    timestamp: '1 month ago',
  },
};
