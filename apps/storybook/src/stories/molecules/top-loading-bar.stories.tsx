'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import { TopLoadingBarIndicator } from '@kit/ui/top-loading-bar-indicator';

const meta = {
  title: 'Molecules/TopLoadingBar',
  component: TopLoadingBarIndicator,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof TopLoadingBarIndicator>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <div className="min-h-[200px] p-4">
      <TopLoadingBarIndicator />
      <div className="mt-8 text-center">
        Loading bar will appear at the top of the screen
      </div>
    </div>
  ),
};

export const WithSimulatedNavigation: Story = {
  render: () => {
    return (
      <div className="min-h-[200px] space-y-4 p-4">
        <TopLoadingBarIndicator />
        <div className="flex justify-center gap-4">
          <Button
            onClick={() => {
              // Simulate page navigation
              window.location.reload();
            }}
          >
            Simulate Navigation
          </Button>
        </div>
        <div className="text-muted-foreground text-center text-sm">
          Click the button to see the loading bar in action
        </div>
      </div>
    );
  },
};
