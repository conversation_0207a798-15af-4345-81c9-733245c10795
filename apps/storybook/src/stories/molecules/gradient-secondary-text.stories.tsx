import type { Meta, StoryObj } from '@storybook/nextjs';

import { GradientSecondaryText } from '@kit/ui/dojo/atoms/gradient-secondary-text';

const meta = {
  title: 'Molecules/Marketing/GradientSecondaryText',
  component: GradientSecondaryText,
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
      description: 'Text content to display with gradient effect',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
  },
} satisfies Meta<typeof GradientSecondaryText>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Gradient Secondary Text',
  },
};

export const LongText: Story = {
  args: {
    children:
      'This is a longer piece of text to demonstrate how the gradient secondary text component handles multiple lines of content.',
  },
};

export const CustomClass: Story = {
  args: {
    children: 'Custom Styled Text',
    className: 'text-2xl font-bold',
  },
};
