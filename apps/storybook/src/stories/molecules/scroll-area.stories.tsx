import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { ScrollArea } from '@kit/ui/scroll-area';

const meta = {
  title: 'Molecules/ScrollArea',
  component: ScrollArea,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof ScrollArea>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default scroll area with vertical content
export const Default: Story = {
  render: () => (
    <ScrollArea className="h-[200px] w-[350px] rounded-md border p-4">
      <div className="space-y-4">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <h4 className="text-sm font-medium leading-none">
              Section {i + 1}
            </h4>
            <p className="text-muted-foreground text-sm">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
        ))}
      </div>
    </ScrollArea>
  ),
};

// Horizontal scroll area
export const Horizontal: Story = {
  render: () => (
    <ScrollArea className="w-[350px] whitespace-nowrap rounded-md border p-4">
      <div className="flex space-x-4">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="w-[200px] shrink-0 rounded-md border border-dashed p-4"
          >
            <h4 className="mb-2 text-sm font-medium leading-none">
              Card {i + 1}
            </h4>
            <p className="text-muted-foreground text-sm">
              Scroll horizontally to see more cards.
            </p>
          </div>
        ))}
      </div>
    </ScrollArea>
  ),
};

// Scroll area with nested content
export const Nested: Story = {
  render: () => (
    <ScrollArea className="h-[400px] w-[350px] rounded-md border p-4">
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <h4 className="text-sm font-medium leading-none">Group {i + 1}</h4>
            <ScrollArea className="h-[200px] w-full rounded-md border p-4">
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, j) => (
                  <div key={j} className="space-y-1">
                    <h5 className="text-sm font-medium leading-none">
                      Item {j + 1}
                    </h5>
                    <p className="text-muted-foreground text-sm">
                      Nested scrollable content.
                    </p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        ))}
      </div>
    </ScrollArea>
  ),
};

// Scroll area with custom styling
export const CustomStyling: Story = {
  render: () => (
    <ScrollArea className="bg-muted h-[300px] w-[350px] rounded-md border p-4">
      <div className="space-y-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="bg-background hover:bg-accent rounded-lg p-4 shadow-xs transition-colors"
          >
            <h4 className="mb-2 text-sm font-medium leading-none">
              Custom Item {i + 1}
            </h4>
            <p className="text-muted-foreground text-sm">
              Styled scrollable content with hover effects.
            </p>
          </div>
        ))}
      </div>
    </ScrollArea>
  ),
};
