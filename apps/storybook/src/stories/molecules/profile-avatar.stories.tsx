import type { Meta, StoryObj } from '@storybook/nextjs';

import { ProfileAvatar } from '@kit/ui/profile-avatar';

const meta = {
  title: 'Molecules/ProfileAvatar',
  component: ProfileAvatar,
  tags: ['autodocs'],
  argTypes: {
    displayName: {
      control: 'text',
      description: 'Display name for the avatar (used for initials)',
    },
    pictureUrl: {
      control: 'text',
      description: 'URL of the profile picture',
    },
    text: {
      control: 'text',
      description: 'Text to display as fallback (alternative to displayName)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes for the avatar',
    },
    fallbackClassName: {
      control: 'text',
      description: 'Additional CSS classes for the fallback content',
    },
  },
} satisfies Meta<typeof ProfileAvatar>;

export default meta;
type Story = StoryObj<typeof ProfileAvatar>;

export const WithImage: Story = {
  args: {
    displayName: 'John Doe',
    pictureUrl: 'https://avatars.githubusercontent.com/u/1',
  },
};

export const WithInitials: Story = {
  args: {
    displayName: '<PERSON>',
  },
};

export const WithText: Story = {
  args: {
    text: 'Guest',
  },
};

export const CustomSize: Story = {
  args: {
    displayName: 'John Doe',
    className: 'h-16 w-16',
  },
};

export const CustomFallbackStyle: Story = {
  args: {
    displayName: 'John Doe',
    fallbackClassName: 'bg-primary text-primary-foreground',
  },
};

export const LoadingState: Story = {
  args: {
    displayName: 'John Doe',
    pictureUrl: 'https://avatars.githubusercontent.com/loading',
  },
};

export const BrokenImage: Story = {
  args: {
    displayName: 'John Doe',
    pictureUrl: 'https://invalid-url.com/image.jpg',
  },
};

export const MultipleAvatars: Story = {
  render: () => (
    <div className="flex gap-4">
      <ProfileAvatar
        displayName="John Doe"
        pictureUrl="https://avatars.githubusercontent.com/u/1"
      />
      <ProfileAvatar displayName="Jane Smith" />
      <ProfileAvatar text="Guest" />
      <ProfileAvatar
        displayName="Alice Johnson"
        className="h-12 w-12"
        fallbackClassName="bg-primary text-primary-foreground"
      />
    </div>
  ),
};
