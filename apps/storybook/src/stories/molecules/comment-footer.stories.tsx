import type { Meta, StoryObj } from '@storybook/nextjs';
import { MessageSquare } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { CommentFooter } from '@kit/ui/dojo/molecules/comment-footer';
import { ReactionsGroup } from '@kit/ui/dojo/molecules/reactions-group';

const meta = {
  title: 'Molecules/CommentFooter',
  component: CommentFooter,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CommentFooter>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockReactionTypes = [
  { value: '+1', icon: '+1' },
  { value: 'heart', icon: 'heart' },
];

const mockReactionCounts = {
  '+1': { count: 5, user_reacted: true },
  heart: { count: 2, user_reacted: false },
};

export const Default: Story = {
  args: {
    leftContent: (
      <ReactionsGroup
        reactionTypes={mockReactionTypes}
        reactionCounts={mockReactionCounts}
        onReaction={() => {}}
      />
    ),
    rightContent: (
      <Button variant="ghost" size="sm">
        <MessageSquare className="mr-2 h-4 w-4" />
        Reply
      </Button>
    ),
  },
};

export const WithoutReactions: Story = {
  args: {
    rightContent: (
      <Button variant="ghost" size="sm">
        <MessageSquare className="mr-2 h-4 w-4" />
        Reply
      </Button>
    ),
  },
};

export const WithoutReplyButton: Story = {
  args: {
    leftContent: (
      <ReactionsGroup
        reactionTypes={mockReactionTypes}
        reactionCounts={mockReactionCounts}
        onReaction={() => {}}
      />
    ),
  },
};

export const Empty: Story = {
  args: {},
};
