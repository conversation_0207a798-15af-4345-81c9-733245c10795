import { Can<PERSON>, Controls, Meta } from '@storybook/addon-docs/blocks';

import { SocialLinks } from '@kit/ui/dojo/molecules/social-links';

import * as SocialLinksStories from './social-links.stories';

<Meta of={SocialLinksStories} />

# Social Links

The Social Links component displays a collection of social media profile links with appropriate icons.

## Usage

```tsx
import { SocialLinks } from '@kit/ui/dojo/molecules/social-links';

export function Profile() {
  return (
    <SocialLinks
      socials={[
        { name: 'github', url: 'https://github.com/username' },
        { name: 'twitter', url: 'https://twitter.com/username' },
        { name: 'linkedin', url: 'https://linkedin.com/in/username' },
      ]}
    />
  );
}
```

## Examples

### Default

<Canvas of={SocialLinksStories.Default} />

### Limited Platforms

<Canvas of={SocialLinksStories.Limited} />

### All Supported Platforms

<Canvas of={SocialLinksStories.AllPlatforms} />

### Custom Styling

<Canvas of={SocialLinksStories.CustomStyling} />

## API Reference

<Controls />

## Supported Social Platforms

The component supports the following social media platforms:

- GitHub
- Twitter
- LinkedIn
- Instagram
- Facebook
- YouTube
- TikTok
- Website (generic website link)

Each platform is displayed with its corresponding FontAwesome icon. If a platform is not recognized, a generic external link icon will be shown.
