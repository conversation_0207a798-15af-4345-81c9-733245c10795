import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import { EmojiPicker } from '@kit/ui/dojo/molecules/emoji-picker';

const meta = {
  title: 'Molecules/EmojiPicker',
  component: EmojiPicker,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onChange: { action: 'onChange' },
    currentIcon: {
      control: 'text',
      description: 'Current selected emoji',
    },
  },
} satisfies Meta<typeof EmojiPicker>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default emoji picker
export const Default: Story = {
  args: {
    onChange: (emoji: string) => console.log('Emoji selected:', emoji),
  },
  render: (args) => {
    return (
      <div className="w-full max-w-sm">
        <EmojiPicker {...args} />
      </div>
    );
  },
};

// Emoji picker with trigger button
export const WithTrigger: Story = {
  args: {
    onChange: (emoji: string) => console.log('Emoji selected:', emoji),
  },
  render: function Wrapper(args) {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="flex flex-col items-center gap-4">
        <Button onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? 'Close' : 'Open'} Emoji Picker
        </Button>
        {isOpen && (
          <div className="w-full max-w-sm">
            <EmojiPicker
              {...args}
              onChange={(emoji) => {
                args.onChange(emoji);
                setIsOpen(false);
              }}
            />
          </div>
        )}
      </div>
    );
  },
};

// Emoji picker with selected emoji display
export const WithSelectedDisplay: Story = {
  args: {
    onChange: (emoji: string) => console.log('Emoji selected:', emoji),
  },
  render: function Wrapper(args) {
    const [selectedEmoji, setSelectedEmoji] = useState<string>('');

    return (
      <div className="flex flex-col items-center gap-4">
        <div className="text-center">
          <p className="text-muted-foreground mb-2 text-sm">Selected Emoji:</p>
          <div className="flex min-h-10 items-center justify-center text-4xl">
            {selectedEmoji || '👋'}
          </div>
        </div>
        <div className="w-full max-w-sm">
          <EmojiPicker
            {...args}
            onChange={(emoji) => {
              setSelectedEmoji(emoji);
              args.onChange(emoji);
            }}
          />
        </div>
      </div>
    );
  },
};

// Emoji picker in a form context
export const InForm: Story = {
  args: {
    onChange: (emoji: string) => console.log('Emoji selected:', emoji),
  },
  render: function Wrapper(args) {
    const [message, setMessage] = useState('');
    const [isPickerOpen, setIsPickerOpen] = useState(false);

    return (
      <div className="w-full max-w-md space-y-4">
        <div className="flex items-start gap-2">
          <textarea
            className="flex-1 rounded-md border p-2"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            rows={3}
          />
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsPickerOpen(!isPickerOpen)}
          >
            😊
          </Button>
        </div>
        {isPickerOpen && (
          <div className="w-full">
            <EmojiPicker
              {...args}
              onChange={(emoji) => {
                setMessage((prev) => prev + emoji);
                setIsPickerOpen(false);
                args.onChange(emoji);
              }}
            />
          </div>
        )}
      </div>
    );
  },
};
