import { <PERSON><PERSON>, Meta } from '@storybook/addon-docs/blocks';

import * as ToggleGroupStories from './toggle-group.stories';

<Meta of={ToggleGroupStories} />

# Toggle Group

A group of toggle buttons that can be used to select a single value or multiple values.

## Features

- Single or multiple selection modes
- Multiple variants (default, outline)
- Three sizes (sm, default, lg)
- Support for icons and text
- Keyboard accessible
- Screen reader friendly
- Customizable styling
- Dark mode compatible
- Disabled state support

## Usage

```tsx
import { ToggleGroup, ToggleGroupItem } from '@kit/ui/toggle-group';
import { Bold, Italic, Underline } from 'lucide-react';

// Single selection
<ToggleGroup type="single" aria-label="Text formatting">
  <ToggleGroupItem value="bold" aria-label="Toggle bold">
    <Bold className="h-4 w-4" />
  </ToggleGroupItem>
  <ToggleGroupItem value="italic" aria-label="Toggle italic">
    <Italic className="h-4 w-4" />
  </ToggleGroupItem>
  <ToggleGroupItem value="underline" aria-label="Toggle underline">
    <Underline className="h-4 w-4" />
  </ToggleGroupItem>
</ToggleGroup>

// Multiple selection
<ToggleGroup type="multiple" aria-label="Text formatting">
  <ToggleGroupItem value="bold" aria-label="Toggle bold">
    <Bold className="h-4 w-4" />
  </ToggleGroupItem>
  <ToggleGroupItem value="italic" aria-label="Toggle italic">
    <Italic className="h-4 w-4" />
  </ToggleGroupItem>
  <ToggleGroupItem value="underline" aria-label="Toggle underline">
    <Underline className="h-4 w-4" />
  </ToggleGroupItem>
</ToggleGroup>
```

## Examples

### Default

A basic toggle group with single selection.

<Canvas of={ToggleGroupStories.Default} />

### Multiple Selection

Toggle group with multiple selection enabled.

<Canvas of={ToggleGroupStories.Multiple} />

### Outline Variant

Toggle group with an outline style.

<Canvas of={ToggleGroupStories.Outline} />

### Small Size

Toggle group with a smaller size.

<Canvas of={ToggleGroupStories.Small} />

### Large Size

Toggle group with a larger size.

<Canvas of={ToggleGroupStories.Large} />

### Disabled State

Toggle group in a disabled state.

<Canvas of={ToggleGroupStories.Disabled} />

### With Default Value

Toggle group with a pre-selected value.

<Canvas of={ToggleGroupStories.WithDefaultValue} />

## Accessibility

The Toggle Group component follows WAI-ARIA guidelines for toggle buttons:

- Uses `role="group"` for the container
- Uses `role="button"` with `aria-pressed` state for items
- Supports keyboard navigation
- Requires `aria-label` for the group and items
- Maintains focus states
- Announces state changes to screen readers

### Keyboard Interactions

- `Tab`: Moves focus to the toggle group
- `Space` or `Enter`: Activates the focused toggle
- `Arrow Keys`: Moves focus between toggles
- `Shift + Tab`: Moves focus to the previous focusable element

## Design Considerations

1. **Visual Feedback**: Clear indication of selected state
2. **Touch Target**: Adequate size for touch interaction
3. **Spacing**: Consistent spacing between toggles
4. **Color Contrast**: Sufficient contrast in all states
5. **Focus Indicator**: Clear focus ring when focused

## Best Practices

1. **Clear Labels**: Always provide clear labels through text or aria-label
2. **Icon Usage**: Use clear, recognizable icons
3. **State Indication**: Ensure the current state is clearly visible
4. **Grouping**: Group related toggles together
5. **Consistency**: Maintain consistent styling across your application

## Form Integration

Example of using toggle group in a form:

```tsx
import { useForm } from 'react-hook-form';

import { ToggleGroup, ToggleGroupItem } from '@kit/ui/toggle-group';

function Form() {
  const form = useForm();

  return (
    <form>
      <div className="space-y-2">
        <label className="text-sm font-medium">Text Formatting</label>
        <ToggleGroup
          type="multiple"
          value={form.watch('formatting')}
          onValueChange={(value) => form.setValue('formatting', value)}
          aria-label="Text formatting"
        >
          <ToggleGroupItem value="bold" aria-label="Toggle bold">
            <Bold className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="italic" aria-label="Toggle italic">
            <Italic className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="underline" aria-label="Toggle underline">
            <Underline className="h-4 w-4" />
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </form>
  );
}
```

## Customization

The Toggle Group component can be customized using Tailwind CSS classes:

```tsx
// Custom colors
<ToggleGroup className="bg-primary/5">
  <ToggleGroupItem className="data-[state=on]:bg-primary data-[state=on]:text-primary-foreground" />
</ToggleGroup>

// Custom spacing
<ToggleGroup className="gap-2">
  <ToggleGroupItem className="px-6" />
</ToggleGroup>

// Custom border
<ToggleGroup className="border-2 border-primary">
  <ToggleGroupItem className="border-2" />
</ToggleGroup>

// Custom hover effect
<ToggleGroup>
  <ToggleGroupItem className="hover:bg-primary/90" />
</ToggleGroup>
```
