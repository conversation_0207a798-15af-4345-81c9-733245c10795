import { Canvas, Meta } from '@storybook/addon-docs/blocks';

import * as ProfileAvatarStories from './profile-avatar.stories';

<Meta of={ProfileAvatarStories} />

# Profile Avatar

A versatile avatar component that displays user profile pictures with fallback to initials or custom text.

## Features

- **Image Support**: Display profile pictures with automatic fallback
- **Fallback Options**: Show initials from display name or custom text
- **Customizable Size**: Flexible sizing through className
- **Custom Styling**: Support for custom styles on both avatar and fallback
- **Loading States**: Graceful handling of loading and error states
- **Animations**: Smooth fade-in animations for fallback content
- **Dark Mode Compatible**: Works seamlessly in both light and dark themes
- **Accessibility**: Screen reader friendly with proper ARIA attributes

## Usage

```tsx
import { ProfileAvatar } from '@kit/ui/profile-avatar';

// With profile picture
<ProfileAvatar
  displayName="John Doe"
  pictureUrl="https://example.com/avatar.jpg"
/>

// With initials fallback
<ProfileAvatar displayName="John Doe" />

// With custom text
<ProfileAvatar text="Guest" />

// With custom size
<ProfileAvatar
  displayName="John Doe"
  className="h-16 w-16"
/>

// With custom fallback style
<ProfileAvatar
  displayName="John Doe"
  fallbackClassName="bg-primary text-primary-foreground"
/>
```

## Examples

### With Profile Picture

Avatar with a profile picture URL.

<Canvas of={ProfileAvatarStories.WithImage} />

### With Initials

Avatar showing initials when no picture is provided.

<Canvas of={ProfileAvatarStories.WithInitials} />

### With Custom Text

Avatar with custom text instead of initials.

<Canvas of={ProfileAvatarStories.WithText} />

### Custom Size

Avatar with a custom size using Tailwind classes.

<Canvas of={ProfileAvatarStories.CustomSize} />

### Custom Fallback Style

Avatar with custom styling for the fallback content.

<Canvas of={ProfileAvatarStories.CustomFallbackStyle} />

### Loading State

Avatar handling loading state gracefully.

<Canvas of={ProfileAvatarStories.LoadingState} />

### Broken Image

Avatar showing fallback when image fails to load.

<Canvas of={ProfileAvatarStories.BrokenImage} />

### Multiple Avatars

Different avatar variations used together.

<Canvas of={ProfileAvatarStories.MultipleAvatars} />

## Accessibility

The Profile Avatar component follows accessibility best practices:

1. Uses semantic HTML with proper ARIA attributes
2. Provides text alternatives for images
3. Maintains proper color contrast for fallback content
4. Supports keyboard navigation in interactive contexts
5. Includes proper focus states when used in interactive elements

## Design Considerations

1. **Image Quality**: Use appropriately sized and optimized images
2. **Fallback Readability**: Ensure fallback text is legible
3. **Loading Experience**: Smooth transition between states
4. **Error Handling**: Graceful fallback for failed image loads
5. **Responsive Design**: Maintains aspect ratio at different sizes

## Best Practices

1. **Image URLs**: Use secure (HTTPS) URLs for profile pictures
2. **Display Names**: Keep display names concise for initials
3. **Custom Text**: Use short text for custom fallback content
4. **Sizing**: Use consistent sizes within your application
5. **Error States**: Always provide meaningful fallback content

## Customization

The Profile Avatar can be customized using Tailwind CSS classes:

```tsx
// Custom size
<ProfileAvatar
  displayName="John Doe"
  className="h-20 w-20"
/>

// Custom background
<ProfileAvatar
  displayName="John Doe"
  fallbackClassName="bg-linear-to-r from-primary to-secondary"
/>

// Custom border
<ProfileAvatar
  displayName="John Doe"
  className="border-2 border-primary"
/>

// Custom hover effect
<ProfileAvatar
  displayName="John Doe"
  className="hover:scale-110 transition-transform"
/>

// Group styling
<div className="flex -space-x-2">
  <ProfileAvatar displayName="User 1" className="border-2 border-background" />
  <ProfileAvatar displayName="User 2" className="border-2 border-background" />
</div>
```

## API Reference

### Props

The component accepts two types of prop combinations:

#### Session Props

- `displayName`: User's display name (used for initials)
- `pictureUrl`: URL of the profile picture (optional)
- `className`: Additional CSS classes for the avatar
- `fallbackClassName`: Additional CSS classes for the fallback content

#### Text Props

- `text`: Custom text to display as fallback
- `className`: Additional CSS classes for the avatar
- `fallbackClassName`: Additional CSS classes for the fallback content

### TypeScript Interface

```tsx
type SessionProps = {
  displayName: string | null;
  pictureUrl?: string | null;
};

type TextProps = {
  text: string;
};

type ProfileAvatarProps = (SessionProps | TextProps) & {
  className?: string;
  fallbackClassName?: string;
};
```
