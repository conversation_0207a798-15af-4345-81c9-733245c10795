'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Settings2, User } from 'lucide-react';

import {
  <PERSON><PERSON>utton,
  CardButtonContent,
  CardButtonFooter,
  CardButtonHeader,
  CardButtonTitle,
} from '@kit/ui/card-button';

const meta = {
  title: 'Molecules/CardButton',
  component: CardButton,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    asChild: {
      control: 'boolean',
      description: 'Whether to render as a child component',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof CardButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <>
        <CardButtonHeader>
          <CardButtonTitle>Default Card Button</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>Click me to see what happens!</CardButtonContent>
      </>
    ),
  },
};

export const WithIcon: Story = {
  args: {
    children: (
      <>
        <CardButtonHeader>
          <Settings2 className="h-5 w-5" />
          <CardButtonTitle>Settings</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>
          Configure your application settings
        </CardButtonContent>
      </>
    ),
  },
};

export const WithFooter: Story = {
  args: {
    children: (
      <>
        <CardButtonHeader>
          <User className="h-5 w-5" />
          <CardButtonTitle>Profile</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>
          View and edit your profile information
        </CardButtonContent>
        <CardButtonFooter>Last updated: 2 days ago</CardButtonFooter>
      </>
    ),
  },
};

export const WithoutArrow: Story = {
  args: {
    children: (
      <>
        <CardButtonHeader displayArrow={false}>
          <CardButtonTitle>No Arrow Button</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>
          This button doesn&apos;t show an arrow on hover
        </CardButtonContent>
      </>
    ),
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: (
      <>
        <CardButtonHeader>
          <CardButtonTitle>Disabled Button</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>This button cannot be clicked</CardButtonContent>
      </>
    ),
  },
};

export const CustomStyles: Story = {
  args: {
    className: 'bg-primary text-primary-foreground hover:bg-primary/90',
    children: (
      <>
        <CardButtonHeader>
          <CardButtonTitle>Custom Styled Button</CardButtonTitle>
        </CardButtonHeader>
        <CardButtonContent>
          This button has custom background and text colors
        </CardButtonContent>
      </>
    ),
  },
};
