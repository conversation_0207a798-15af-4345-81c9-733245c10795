'use client';

import { useEffect, useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { LazyRender } from '@kit/ui/lazy-render';

const meta = {
  title: 'Molecules/LazyRender',
  component: LazyRender,
  tags: ['autodocs'],
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    threshold: {
      control: 'number',
      description: 'Intersection observer threshold (0-1)',
    },
    rootMargin: {
      control: 'text',
      description: 'Intersection observer root margin',
    },
    onVisible: {
      description: 'Callback when component becomes visible',
    },
  },
} satisfies Meta<typeof LazyRender>;

export default meta;
type Story = StoryObj<typeof meta>;

const LazyLoadedContent = () => {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    // Simulate content loading
    const timer = setTimeout(() => setLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-4 rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Lazy Loaded Content</h3>
        <span className="text-muted-foreground text-sm">
          {loaded ? 'Loaded' : 'Loading...'}
        </span>
      </div>
      {loaded ? (
        <p className="text-muted-foreground">
          This content was lazily rendered when it came into view.
        </p>
      ) : (
        <div className="bg-muted h-4 w-3/4 animate-pulse rounded" />
      )}
    </div>
  );
};

export const Default: Story = {
  args: {
    threshold: 0.1,
    rootMargin: '50px',
  },
  render: (args) => (
    <div className="space-y-4">
      <div className="h-[300px] overflow-y-auto rounded-lg border p-4">
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Scroll down to see the lazy loaded content...
          </p>
          <div className="h-[400px]" />
          <LazyRender {...args}>
            <LazyLoadedContent />
          </LazyRender>
        </div>
      </div>
    </div>
  ),
};

export const WithCallback: Story = {
  args: {
    threshold: 0.1,
    rootMargin: '50px',
  },
  render: (args) => {
    const [visibilityCount, setVisibilityCount] = useState(0);

    return (
      <div className="space-y-4">
        <div className="text-muted-foreground text-sm">
          Times content became visible: {visibilityCount}
        </div>
        <div className="h-[300px] overflow-y-auto rounded-lg border p-4">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Scroll down to see the lazy loaded content...
            </p>
            <div className="h-[400px]" />
            <LazyRender
              {...args}
              onVisible={() => setVisibilityCount((count) => count + 1)}
            >
              <LazyLoadedContent />
            </LazyRender>
          </div>
        </div>
      </div>
    );
  },
};

export const DifferentThresholds: Story = {
  render: () => (
    <div className="space-y-8">
      {[0.1, 0.5, 1.0].map((threshold) => (
        <div key={threshold} className="space-y-2">
          <div className="text-sm font-medium">
            Threshold: {threshold * 100}% visibility required
          </div>
          <div className="h-[200px] overflow-y-auto rounded-lg border p-4">
            <div className="space-y-4">
              <p className="text-muted-foreground">Scroll down...</p>
              <div className="h-[200px]" />
              <LazyRender threshold={threshold}>
                <LazyLoadedContent />
              </LazyRender>
            </div>
          </div>
        </div>
      ))}
    </div>
  ),
};

export const DifferentRootMargins: Story = {
  render: () => (
    <div className="space-y-8">
      {['0px', '100px', '200px'].map((margin) => (
        <div key={margin} className="space-y-2">
          <div className="text-sm font-medium">Root Margin: {margin}</div>
          <div className="h-[200px] overflow-y-auto rounded-lg border p-4">
            <div className="space-y-4">
              <p className="text-muted-foreground">Scroll down...</p>
              <div className="h-[200px]" />
              <LazyRender rootMargin={margin}>
                <LazyLoadedContent />
              </LazyRender>
            </div>
          </div>
        </div>
      ))}
    </div>
  ),
};
