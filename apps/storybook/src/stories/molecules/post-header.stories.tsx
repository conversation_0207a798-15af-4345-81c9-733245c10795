import React from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { MoreVertical, Pin } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Button } from '@kit/ui/button';
import { PostHeader } from '@kit/ui/dojo/molecules/post-header';

const meta = {
  title: 'Molecules/PostHeader',
  component: PostHeader,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PostHeader>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to create Avatar (copied from comment-header.stories)
const createAvatar = (args: {
  authorFirstName: string;
  authorLastName: string;
  authorPictureUrl?: string;
}) => (
  <div className="flex items-center gap-2">
    <Avatar>
      {args.authorPictureUrl && <AvatarImage src={args.authorPictureUrl} />}
      <AvatarFallback>
        {args.authorFirstName?.charAt(0)}
        {args.authorLastName?.charAt(0)}
      </AvatarFallback>
    </Avatar>
    {/* Removed name span as PostHeader doesn't seem to need it here */}
  </div>
);

export const Default: Story = {
  args: {
    title: 'My First Post',
    category: 'General Discussion',
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300',
    }),
  },
};

export const WithBadges: Story = {
  args: {
    title: 'Feature Request: Dark Mode',
    category: 'Feedback',
    badges: [
      { label: 'Draft', variant: 'outline' },
      {
        label: 'Important',
        variant: 'default',
        className: 'bg-red-500 text-white',
      },
    ],
    avatarChildren: createAvatar({
      authorFirstName: 'Jane',
      authorLastName: 'Smith',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=5',
    }),
  },
};

export const WithActions: Story = {
  args: {
    title: 'How to integrate Stripe?',
    category: 'Integrations',
    avatarChildren: createAvatar({
      authorFirstName: 'Alice',
      authorLastName: 'Johnson',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=10',
    }),
    actions: (
      <>
        <Button variant="ghost" size="icon">
          <Pin className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </>
    ),
  },
};

export const Complete: Story = {
  args: {
    title: 'Complete Example',
    category: 'Tutorials',
    badges: [{ label: 'Pinned', variant: 'secondary' }],
    avatarChildren: createAvatar({
      authorFirstName: 'Bob',
      authorLastName: 'Wilson',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=15',
    }),
    actions: (
      <Button variant="outline" size="sm">
        Follow
      </Button>
    ),
    'data-test': 'post-header-complete',
  },
};

export const LongTitle: Story = {
  args: {
    title:
      'This is a very long post title that should wrap properly and maintain good readability within the header component to ensure user experience is not degraded',
    category: 'UI/UX',
    avatarChildren: createAvatar({
      authorFirstName: 'Emma',
      authorLastName: 'Davis',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=20',
    }),
  },
};
