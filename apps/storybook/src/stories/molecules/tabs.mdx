import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as TabsStories from './tabs.stories';

<Meta of={TabsStories} />

# Tabs

A set of layered sections of content that are displayed one at a time.

## Features

- Horizontal and vertical orientations
- Keyboard navigation
- Screen reader support
- Disabled states
- Icon support
- Form integration
- Customizable styling
- Automatic activation
- Focus management

## Usage

```tsx
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

export default function MyTabs() {
  return (
    <Tabs defaultValue="account">
      <TabsList>
        <TabsTrigger value="account">Account</TabsTrigger>
        <TabsTrigger value="password">Password</TabsTrigger>
      </TabsList>
      <TabsContent value="account">Account settings...</TabsContent>
      <TabsContent value="password">Password settings...</TabsContent>
    </Tabs>
  );
}
```

## Examples

### Default

Basic usage with multiple tabs.

<Canvas of={TabsStories.Default} />

### With Icons

Tabs with icons for better visual representation.

<Canvas of={TabsStories.WithIcons} />

### With Form

Tabs containing form elements.

<Canvas of={TabsStories.WithForm} />

### Disabled

Tabs with disabled state.

<Canvas of={TabsStories.Disabled} />

## Component API

### Tabs

The root tabs component.

#### Props

- `defaultValue`: string - The value of the tab that should be active when initially rendered
- `value?`: string - The controlled value of the tab that should be active
- `onValueChange?`: (value: string) => void - Event handler called when the value changes
- `orientation?`: "horizontal" | "vertical" - The orientation of the tabs
- `dir?`: "ltr" | "rtl" - The reading direction of the tabs
- `activationMode?`: "automatic" | "manual" - Whether tabs are activated automatically on focus or manually

### TabsList

The container for the tab triggers.

#### Props

- `className?`: string - Additional CSS classes
- `loop?`: boolean - Whether keyboard navigation should loop from last tab to first, and vice versa

### TabsTrigger

The button that activates its associated content.

#### Props

- `value`: string - A unique value that associates the trigger with a content
- `disabled?`: boolean - Whether the trigger is disabled
- `className?`: string - Additional CSS classes

### TabsContent

The content that's associated with a tab trigger.

#### Props

- `value`: string - A unique value that associates the content with a trigger
- `forceMount?`: boolean - Forces mounting when true
- `className?`: string - Additional CSS classes

## Accessibility

The Tabs component follows WAI-ARIA guidelines:

### Keyboard Navigation

- `Tab`: Move focus to the next focusable element
- `Shift + Tab`: Move focus to the previous focusable element
- `ArrowRight/ArrowDown`: Move focus to the next tab
- `ArrowLeft/ArrowUp`: Move focus to the previous tab
- `Home`: Move focus to the first tab
- `End`: Move focus to the last tab
- `Space/Enter`: Activate the tab

### ARIA Attributes

- Uses `role="tablist"` for the list container
- Uses `role="tab"` for triggers
- Uses `role="tabpanel"` for content
- Manages `aria-selected` state
- Provides `aria-controls` and `aria-labelledby`
- Handles `aria-orientation`

## Guidelines

### Usage Guidelines

1. **Content Organization**

   - Group related content
   - Use clear labels
   - Keep content focused
   - Consider tab order

2. **Visual Design**

   - Maintain consistent height
   - Use clear active states
   - Consider hover states
   - Provide visual feedback

3. **Interaction Design**
   - Handle loading states
   - Consider transitions
   - Manage focus states
   - Support keyboard use

### Content Guidelines

1. **Tab Labels**

   - Use clear, concise text
   - Be descriptive
   - Consider icons
   - Maintain consistency

2. **Content Structure**
   - Organize logically
   - Use consistent layouts
   - Consider scrolling
   - Handle overflow

### Error Handling

1. **Form Integration**

   - Preserve form state
   - Handle validation
   - Show error states
   - Maintain context

2. **Edge Cases**
   - Handle long content
   - Manage dynamic tabs
   - Consider loading
   - Handle errors

## Performance Considerations

1. **Content Loading**

   - Lazy load when needed
   - Optimize transitions
   - Manage state
   - Handle async content

2. **Rendering**
   - Minimize re-renders
   - Use content portals
   - Handle unmounting
   - Consider SSR

## Responsive Design

1. **Mobile Considerations**

   - Touch-friendly targets
   - Handle orientation
   - Consider gestures
   - Test on devices

2. **Layout Adaptations**
   - Adjust for viewport
   - Handle overflow
   - Consider breakpoints
   - Maintain usability

<Controls />{' '}
