import * as React from 'react';

import type { Meta } from '@storybook/nextjs';
import { Settings2 } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';

const meta = {
  title: 'Molecules/Popover',
  component: Popover,
  tags: ['autodocs'],
  argTypes: {
    defaultOpen: {
      control: 'boolean',
      description:
        'The open state of the popover when it is initially rendered',
    },
    open: {
      control: 'boolean',
      description: 'The controlled open state of the popover',
    },
    onOpenChange: {
      description: 'Event handler called when the open state changes',
    },
  },
} satisfies Meta<typeof Popover>;

export default meta;

export const Default = {
  render: () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline">Open Popover</Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Dimensions</h4>
            <p className="text-muted-foreground text-sm">
              Set the dimensions for the layer.
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  ),
};

export const WithIcon = {
  render: () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings2 className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Settings</h4>
            <p className="text-muted-foreground text-sm">
              Manage your application settings.
            </p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  ),
};

export const WithForm = {
  render: () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline">Edit Profile</Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <form className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Profile</h4>
            <p className="text-muted-foreground text-sm">
              Update your profile information.
            </p>
          </div>
          <div className="grid gap-2">
            <div className="grid grid-cols-3 items-center gap-4">
              <label htmlFor="name" className="text-sm font-medium">
                Name
              </label>
              <input
                id="name"
                defaultValue="John Doe"
                className="border-input placeholder:text-muted-foreground focus-visible:ring-ring col-span-2 h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-hidden focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <div className="grid grid-cols-3 items-center gap-4">
              <label htmlFor="email" className="text-sm font-medium">
                Email
              </label>
              <input
                id="email"
                defaultValue="<EMAIL>"
                className="border-input placeholder:text-muted-foreground focus-visible:ring-ring col-span-2 h-8 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-colors focus-visible:outline-hidden focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
          <Button type="submit">Save changes</Button>
        </form>
      </PopoverContent>
    </Popover>
  ),
};

export const WithCustomPosition = {
  render: () => (
    <div className="flex items-center justify-center space-x-4">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline">Bottom</Button>
        </PopoverTrigger>
        <PopoverContent>
          <div className="px-1 py-2">
            <div className="text-sm font-medium">Bottom Popover</div>
            <p className="text-muted-foreground text-sm">
              This popover appears at the bottom.
            </p>
          </div>
        </PopoverContent>
      </Popover>

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline">Top</Button>
        </PopoverTrigger>
        <PopoverContent side="top" align="center">
          <div className="px-1 py-2">
            <div className="text-sm font-medium">Top Popover</div>
            <p className="text-muted-foreground text-sm">
              This popover appears at the top.
            </p>
          </div>
        </PopoverContent>
      </Popover>

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline">Left</Button>
        </PopoverTrigger>
        <PopoverContent side="left" align="center">
          <div className="px-1 py-2">
            <div className="text-sm font-medium">Left Popover</div>
            <p className="text-muted-foreground text-sm">
              This popover appears on the left.
            </p>
          </div>
        </PopoverContent>
      </Popover>

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline">Right</Button>
        </PopoverTrigger>
        <PopoverContent side="right" align="center">
          <div className="px-1 py-2">
            <div className="text-sm font-medium">Right Popover</div>
            <p className="text-muted-foreground text-sm">
              This popover appears on the right.
            </p>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  ),
};
