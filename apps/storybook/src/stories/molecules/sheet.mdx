import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as SheetStories from './sheet.stories';

<Meta of={SheetStories} />

# Sheet

A modal dialog that slides in from the edge of the screen. Useful for mobile navigation, settings panels, and more.

## Features

- Slides in from any edge (top, right, bottom, left)
- Modal or non-modal behavior
- Customizable animations
- Keyboard navigation
- Screen reader support
- Overlay with backdrop
- Header and footer sections
- Close button included
- Responsive design

## Usage

```tsx
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@kit/ui/sheet';

export default function MySheet() {
  return (
    <Sheet>
      <SheetTrigger>Open</SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Title</SheetTitle>
          <SheetDescription>Description</SheetDescription>
        </SheetHeader>
        <div>Content</div>
        <SheetFooter>
          <button>Action</button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
```

## Examples

### Default

A basic sheet that slides in from the right side.

<Canvas of={SheetStories.Default} />

### Left Side

A sheet that slides in from the left side, useful for navigation menus.

<Canvas of={SheetStories.LeftSide} />

### Top Side

A sheet that slides in from the top, good for notifications or alerts.

<Canvas of={SheetStories.TopSide} />

### Bottom Side

A sheet that slides in from the bottom, ideal for mobile interactions.

<Canvas of={SheetStories.BottomSide} />

### With Form

A sheet containing a form, demonstrating more complex content.

<Canvas of={SheetStories.WithForm} />

## Component API

### Sheet

The root sheet component.

#### Props

- `defaultOpen?`: boolean - Whether the sheet is open by default
- `open?`: boolean - Controlled open state
- `onOpenChange?`: (open: boolean) => void - Called when open state changes
- `modal?`: boolean - Whether the sheet is modal (blocks interaction with the rest of the page)

### SheetTrigger

The button that opens the sheet.

#### Props

- `asChild?`: boolean - Whether to merge props onto the child element
- `children`: React.ReactNode - The trigger element

### SheetContent

The content of the sheet that slides in.

#### Props

- `side?`: "top" | "right" | "bottom" | "left" - The side to slide in from
- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - The content to display

### SheetHeader

Container for the sheet header content.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - Usually contains SheetTitle and SheetDescription

### SheetFooter

Container for the sheet footer content.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - Usually contains action buttons

### SheetTitle

The title of the sheet.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - The title text

### SheetDescription

A description for the sheet.

#### Props

- `className?`: string - Additional CSS classes
- `children`: React.ReactNode - The description text

## Accessibility

The Sheet component follows WAI-ARIA guidelines for dialogs:

- Uses `role="dialog"` for the content
- Manages focus when opened/closed
- Traps focus within the sheet when open
- Provides proper ARIA labels
- Supports keyboard navigation
- Screen reader announcements

### Keyboard Interactions

- `Escape`: Close the sheet
- `Tab`: Navigate through focusable elements
- `Shift + Tab`: Navigate backward through focusable elements
- `Space/Enter`: Activate buttons and controls

## Guidelines

### Usage Guidelines

1. **Content Organization**

   - Keep content focused and relevant
   - Use clear hierarchy with headers
   - Group related actions together
   - Consider mobile viewports

2. **Interaction Design**

   - Choose appropriate side based on content
   - Consider animation timing
   - Provide clear close actions
   - Handle overlay clicks

3. **Layout Considerations**
   - Use consistent widths
   - Consider content scrolling
   - Maintain readable text width
   - Account for safe areas

### Content Guidelines

1. **Text Content**

   - Use clear, concise titles
   - Provide helpful descriptions
   - Use action-oriented button text
   - Consider internationalization

2. **Visual Hierarchy**
   - Emphasize important actions
   - Use consistent spacing
   - Apply appropriate typography
   - Consider color contrast

### Error Handling

1. **Form Validation**

   - Show clear error messages
   - Validate before closing
   - Preserve form state
   - Handle submission errors

2. **Edge Cases**
   - Handle long content
   - Consider loading states
   - Manage network errors
   - Handle device rotation

## Performance Considerations

1. **Animation Performance**

   - Use hardware-accelerated animations
   - Optimize transition timing
   - Consider reduced motion
   - Monitor frame rates

2. **Content Loading**
   - Lazy load when possible
   - Show loading states
   - Cache content when appropriate
   - Optimize images

## Responsive Design

1. **Mobile Considerations**

   - Full-width on small screens
   - Touch-friendly targets
   - Consider gesture interactions
   - Test on various devices

2. **Viewport Adaptations**
   - Adjust size based on screen
   - Handle orientation changes
   - Consider notch areas
   - Test different breakpoints

<Controls />{' '}
