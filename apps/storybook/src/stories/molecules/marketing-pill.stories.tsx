import type { Meta, StoryObj } from '@storybook/nextjs';

import { Pill } from '@kit/ui/dojo/atoms/marketing-pill';

const meta = {
  title: 'Molecules/Marketing/Pill',
  component: Pill,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Label text displayed in the pill',
    },
    children: {
      control: 'text',
      description: 'Content displayed after the label',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
    asChild: {
      control: 'boolean',
      description: 'Whether to render as a child component',
    },
  },
} satisfies Meta<typeof Pill>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'New',
    children: 'Check out our latest feature!',
  },
};

export const WithLabelOnly: Story = {
  args: {
    label: 'Beta',
  },
};

export const WithChildrenOnly: Story = {
  args: {
    children: 'Available for a limited time.',
  },
};

export const CustomClassName: Story = {
  args: {
    label: 'Sale',
    children: '50% off!',
    className: 'bg-destructive text-destructive-foreground border-destructive',
  },
};

export const AsChild: Story = {
  args: {
    asChild: true,
    label: 'Info',
    children: <a href="#">Learn More</a>,
  },
};

export const RenderProp: Story = {
  render: (args: Story['args']) => (
    <Pill {...args}>
      <span>Render Prop Content</span>
    </Pill>
  ),
  args: {
    label: 'Render',
  },
};
