import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Clock4, DollarSign, Lock, Star } from 'lucide-react';

import { CourseOverlay } from '@kit/ui/dojo/molecules/course-overlay';

const meta = {
  title: 'Course/Molecules/CourseOverlay',
  component: CourseOverlay,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseOverlay>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Private: Story = {
  args: {
    title: 'Private Course',
    description: 'This course is private',
    icon: <Lock className="mb-2 h-8 w-8" />,
    type: 'private',
  },
};

export const Level: Story = {
  args: {
    title: 'Level Required',
    description: 'You need to reach a certain level',
    icon: <Star className="mb-2 h-8 w-8" />,
    type: 'level',
  },
};

export const Paid: Story = {
  args: {
    title: 'Premium Course',
    description: 'This is a paid course',
    icon: <DollarSign className="mb-2 h-8 w-8" />,
    type: 'paid',
  },
};

export const Time: Story = {
  args: {
    title: 'Time Restricted',
    description: 'This course has time restrictions',
    icon: <Clock4 className="mb-2 h-8 w-8" />,
    type: 'time',
  },
};
