import type { Meta, StoryObj } from '@storybook/nextjs';

import { PostContent } from '@kit/ui/dojo/molecules/post-content';

const meta = {
  title: 'Molecules/PostContent',
  component: PostContent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PostContent>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockContent = {
  type: 'doc',
  content: [
    {
      type: 'heading',
      attrs: { level: 1 },
      content: [{ type: 'text', text: 'Welcome to Our Platform' }],
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: 'This is a sample post that demonstrates various content types that can be rendered.',
        },
      ],
    },
    {
      type: 'heading',
      attrs: { level: 2 },
      content: [{ type: 'text', text: 'Rich Text Features' }],
    },
    {
      type: 'paragraph',
      content: [
        { type: 'text', text: 'You can have ' },
        { type: 'text', marks: [{ type: 'bold' }], text: 'bold text' },
        { type: 'text', text: ', ' },
        { type: 'text', marks: [{ type: 'italic' }], text: 'italic text' },
        { type: 'text', text: ', and ' },
        {
          type: 'text',
          marks: [{ type: 'code' }],
          text: 'inline code',
        },
        { type: 'text', text: '.' },
      ],
    },
    {
      type: 'bulletList',
      content: [
        {
          type: 'listItem',
          content: [{ type: 'text', text: 'First bullet point' }],
        },
      ],
    },
    {
      type: 'image',
      attrs: {
        src: 'https://placeholder.com/300x200',
        alt: 'Sample image',
      },
    },
    {
      type: 'codeBlock',
      attrs: { language: 'typescript' },
      content: [
        {
          type: 'text',
          text: 'function greet(name: string) {\n  console.log(`Hello, ${name}!`);\n}',
        },
      ],
    },
    {
      type: 'blockquote',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'This is a blockquote that can be used for important callouts.',
            },
          ],
        },
      ],
    },
  ],
};

export const Default: Story = {
  args: {
    content: mockContent,
  },
};

export const Draft: Story = {
  args: {
    content: mockContent,
    isDraft: true,
  },
};

export const CustomStyle: Story = {
  args: {
    content: mockContent,
    className: 'bg-muted p-4 rounded-lg',
  },
};
