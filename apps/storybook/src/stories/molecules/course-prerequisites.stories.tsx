import type { Meta, StoryObj } from '@storybook/nextjs';

import { CoursePrerequisites } from '@kit/ui/dojo/molecules/course-prerequisites';

const meta = {
  title: 'Course/Molecules/CoursePrerequisites',
  component: CoursePrerequisites,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Displays a list of course prerequisites with completion status. Each prerequisite shows a title and visual indicator of completion status.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CoursePrerequisites>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Shows a mix of completed and incomplete prerequisites, representing a typical course state.',
      },
    },
  },
  args: {
    prerequisites: [
      { id: '1', title: 'Introduction to Programming', completed: true },
      { id: '2', title: 'Basic JavaScript', completed: false },
      { id: '3', title: 'HTML & CSS Fundamentals', completed: true },
    ],
  },
};

export const Empty: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Shows how the component handles an empty prerequisites list. The component should not render in this case.',
      },
    },
  },
  args: {
    prerequisites: [],
  },
};

export const AllCompleted: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Displays all prerequisites as completed, showing the visual state when a user has finished all required courses.',
      },
    },
  },
  args: {
    prerequisites: [
      { id: '1', title: 'Introduction to Programming', completed: true },
      { id: '2', title: 'Basic JavaScript', completed: true },
      { id: '3', title: 'HTML & CSS Fundamentals', completed: true },
    ],
  },
};

export const NoneCompleted: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Shows all prerequisites as incomplete, representing the initial state when a user starts a new learning path.',
      },
    },
  },
  args: {
    prerequisites: [
      { id: '1', title: 'Introduction to Programming', completed: false },
      { id: '2', title: 'Basic JavaScript', completed: false },
      { id: '3', title: 'HTML & CSS Fundamentals', completed: false },
    ],
  },
};
