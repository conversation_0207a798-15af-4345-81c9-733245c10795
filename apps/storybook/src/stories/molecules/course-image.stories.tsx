import type { Meta, StoryObj } from '@storybook/nextjs';

import { CourseImage } from '@kit/ui/dojo/molecules/course-image';

const meta = {
  title: 'Course/Molecules/CourseImage',
  component: CourseImage,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseImage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const WithImage: Story = {
  args: {
    coverUrl: 'https://picsum.photos/400/300',
    title: 'Sample Course',
  },
};

export const WithoutImage: Story = {
  args: {
    title: 'Sample Course',
  },
};

export const CustomHeight: Story = {
  args: {
    coverUrl: 'https://picsum.photos/400/300',
    title: 'Sample Course',
    className: 'h-60',
  },
};
