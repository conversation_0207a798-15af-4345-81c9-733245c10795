import { Can<PERSON>, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as ResizableStories from './resizable.stories';

<Meta of={ResizableStories} />

# Resizable

A set of components for creating resizable panel layouts with drag handles.

## Features

- Horizontal and vertical resizing
- Customizable drag handles
- Min/max size constraints
- Nested panel support
- Keyboard navigation
- Responsive design
- Accessible by default

## Usage

```tsx
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@kit/ui/resizable';

export default function MyLayout() {
  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel defaultSize={25}>
        <div>Sidebar content</div>
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel defaultSize={75}>
        <div>Main content</div>
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
```

## Examples

### Default Layout

A simple horizontal layout with two panels.

<Canvas of={ResizableStories.Default} />

### With Drag Handles

Multiple panels with visible drag handles.

<Canvas of={ResizableStories.WithHandles} />

### Vertical Layout

A vertical layout with three panels.

<Canvas of={ResizableStories.Vertical} />

### With Min/Max Constraints

Panels with size constraints.

<Canvas of={ResizableStories.WithMinMax} />

### Nested Panels

Complex layout with nested panel groups.

<Canvas of={ResizableStories.NestedPanels} />

## Component API

### ResizablePanelGroup

The container component that manages the resizable panels.

#### Props

- `direction`: "horizontal" | "vertical" - The direction of resizing
- `className`: string - Additional CSS classes
- `onLayout`: (sizes: number[]) => void - Callback when layout changes
- `children`: React.ReactNode - Panel and handle components

### ResizablePanel

Individual panel component that can be resized.

#### Props

- `defaultSize`: number - Initial size as a percentage (1-100)
- `minSize`: number - Minimum size as a percentage
- `maxSize`: number - Maximum size as a percentage
- `className`: string - Additional CSS classes
- `children`: React.ReactNode - Panel content

### ResizableHandle

The handle component used to resize panels.

#### Props

- `withHandle`: boolean - Whether to show a visible drag handle
- `className`: string - Additional CSS classes

## Accessibility

The Resizable components follow WAI-ARIA guidelines:

- Proper ARIA roles and attributes
- Keyboard navigation support
- Focus management
- Screen reader announcements

### Keyboard Interactions

- `Tab`: Move focus to the next handle
- `Shift + Tab`: Move focus to the previous handle
- `Arrow keys`: Resize panels when handle is focused
- `Home`: Set panel to minimum size
- `End`: Set panel to maximum size

## Guidelines

### Usage Guidelines

1. **Layout Planning**

   - Plan your layout structure before implementation
   - Consider nested panels for complex layouts
   - Use appropriate direction for content flow

2. **Size Management**

   - Set reasonable default sizes
   - Use min/max constraints to prevent unusable layouts
   - Consider mobile viewports

3. **Performance**
   - Avoid deeply nested panel groups
   - Use appropriate content sizing
   - Consider lazy loading for panel content

### Content Guidelines

1. **Panel Content**

   - Use appropriate padding inside panels
   - Handle content overflow properly
   - Consider loading states

2. **Visual Feedback**
   - Use visible handles for better UX
   - Provide hover and focus states
   - Consider adding resize animations

### Error Handling

1. **Size Validation**

   - Validate min/max constraints
   - Handle invalid size values gracefully
   - Provide fallback sizes

2. **Content Errors**
   - Handle content loading errors
   - Provide error boundaries
   - Show appropriate error messages

## Performance Considerations

1. **Optimization**

   - Use React.memo for complex panel content
   - Implement virtualization for large lists
   - Optimize resize calculations

2. **Loading States**
   - Show loading indicators
   - Use content placeholders
   - Implement progressive loading

## Responsive Design

1. **Mobile Considerations**

   - Adjust panel sizes for small screens
   - Consider collapsing panels
   - Use appropriate touch targets

2. **Breakpoints**
   - Define responsive breakpoints
   - Adjust layout direction
   - Handle orientation changes

<Controls />{' '}
