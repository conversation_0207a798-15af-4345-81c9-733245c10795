import type { Meta, StoryObj } from '@storybook/nextjs';

import { ReactionsGroup } from '@kit/ui/dojo/molecules/reactions-group';

const meta = {
  title: 'Molecules/ReactionsGroup',
  component: ReactionsGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ReactionsGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockReactionCounts = {
  '+1': { count: 5, user_reacted: false },
  '-1': { count: 1, user_reacted: false },
  heart: { count: 3, user_reacted: true },
  smile: { count: 2, user_reacted: false },
  tada: { count: 4, user_reacted: false },
};

const mockReactionTypes = [
  { value: '+1', icon: 'thumbsup' },
  { value: '-1', icon: 'thumbsdown' },
  { value: 'heart', icon: 'heart' },
  { value: 'smile', icon: 'smile' },
  { value: 'tada', icon: 'tada' },
];

export const Default: Story = {
  args: {
    reactionCounts: mockReactionCounts,
    onReaction: (type: string) => {
      console.log('Reaction clicked:', type);
    },
    reactionTypes: mockReactionTypes,
  },
};

export const WithUserReactions: Story = {
  args: {
    reactionCounts: {
      ...mockReactionCounts,
      '+1': { count: 6, user_reacted: true },
      heart: { count: 3, user_reacted: true },
    },
    onReaction: (type: string) => {
      console.log('Reaction clicked:', type);
    },
    reactionTypes: mockReactionTypes,
  },
};

export const Loading: Story = {
  args: {
    reactionCounts: mockReactionCounts,
    onReaction: () => {},
    reactionTypes: mockReactionTypes,
    isLoading: true,
  },
};

export const Disabled: Story = {
  args: {
    reactionCounts: mockReactionCounts,
    onReaction: () => {},
    reactionTypes: mockReactionTypes,
    disabled: true,
  },
};

export const HiddenReactions: Story = {
  args: {
    reactionCounts: mockReactionCounts,
    onReaction: () => {},
    reactionTypes: [
      { value: '+1', icon: 'thumbsup' },
      { value: '-1', icon: 'thumbsdown' },
      { value: 'heart', icon: 'heart', hideFromList: true },
      { value: 'smile', icon: 'smile', hideFromList: true },
    ],
  },
};
