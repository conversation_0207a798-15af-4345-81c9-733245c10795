'use client';

import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { Slider } from '@kit/ui/slider';

const meta = {
  title: 'Molecules/Slider',
  component: Slider,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    defaultValue: {
      control: 'object',
      description: 'The default value(s) of the slider',
    },
    min: {
      control: 'number',
      description: 'The minimum value of the slider',
    },
    max: {
      control: 'number',
      description: 'The maximum value of the slider',
    },
    step: {
      control: 'number',
      description: 'The step value of the slider',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the slider is disabled',
    },
  },
} satisfies Meta<typeof Slider>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: [50],
    min: 0,
    max: 100,
    step: 1,
  },
  render: (args) => (
    <div className="min-w-[300px] p-6">
      <Slider
        defaultValue={args.defaultValue}
        min={args.min}
        max={args.max}
        step={args.step}
      />
    </div>
  ),
};

export const RangeSlider: Story = {
  args: {
    defaultValue: [25, 75],
    min: 0,
    max: 100,
    step: 1,
  },
  render: (args) => (
    <div className="min-w-[300px] p-6">
      <Slider
        defaultValue={args.defaultValue}
        min={args.min}
        max={args.max}
        step={args.step}
      />
    </div>
  ),
};

export const SteppedSlider: Story = {
  args: {
    defaultValue: [0],
    min: 0,
    max: 100,
    step: 10,
  },
  render: (args) => (
    <div className="min-w-[300px] p-6">
      <Slider
        defaultValue={args.defaultValue}
        min={args.min}
        max={args.max}
        step={args.step}
      />
    </div>
  ),
};

export const DisabledSlider: Story = {
  args: {
    defaultValue: [50],
    min: 0,
    max: 100,
    step: 1,
    disabled: true,
  },
  render: (args) => (
    <div className="min-w-[300px] p-6">
      <Slider
        defaultValue={args.defaultValue}
        min={args.min}
        max={args.max}
        step={args.step}
        disabled={args.disabled}
      />
    </div>
  ),
};

export const WithLabels: Story = {
  args: {
    defaultValue: [50],
    min: 0,
    max: 100,
    step: 1,
  },
  render: (args) => (
    <div className="min-w-[300px] p-6">
      <div className="space-y-4">
        <div className="flex justify-between">
          <label className="text-muted-foreground text-sm">Volume</label>
          <span className="text-muted-foreground text-sm">
            {args.defaultValue}%
          </span>
        </div>
        <Slider
          defaultValue={args.defaultValue}
          min={args.min}
          max={args.max}
          step={args.step}
        />
      </div>
    </div>
  ),
};
