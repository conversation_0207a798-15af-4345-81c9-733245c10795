import type { Meta, StoryObj } from '@storybook/nextjs';

import { ConnectRequirements } from '@kit/ui/dojo/molecules/connect-requirements';

const meta: Meta<typeof ConnectRequirements> = {
  title: 'Molecules/ConnectRequirements',
  component: ConnectRequirements,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ConnectRequirements>;

export const WithRequirements: Story = {
  args: {
    requirements: {
      currentlyDue: [
        'external_account',
        'business_profile_website',
        'business_profile_description',
      ],
      eventuallyDue: ['business_type', 'business_profile_mcc'],
      pendingVerification: ['company_tax_id'],
      currentDeadline: Date.now() / 1000 + 86400 * 14, // 14 days from now
    },
  },
};

export const WithDisabledReason: Story = {
  args: {
    requirements: {
      currentlyDue: ['external_account', 'business_profile_website'],
      disabledReason: 'requirements.past_due',
      currentDeadline: Date.now() / 1000 + 86400 * 7, // 7 days from now
    },
  },
};

export const Empty: Story = {
  args: {
    requirements: {},
  },
};
