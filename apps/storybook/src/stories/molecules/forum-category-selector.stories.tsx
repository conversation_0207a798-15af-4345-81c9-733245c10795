import type { Meta, StoryObj } from '@storybook/nextjs';

import { ForumCategorySelector } from '@kit/ui/dojo/molecules/forum-category-selector';

const meta = {
  title: 'Molecules/ForumCategorySelector',
  component: ForumCategorySelector,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ForumCategorySelector>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockCategories = [
  {
    id: 'general',
    name: 'General Discussion',
    icon: '💬',
  },
  {
    id: 'help',
    name: 'Help & Support',
    icon: '❓',
  },
  {
    id: 'feedback',
    name: 'Feedback',
    icon: '📝',
  },
];

export const Default: Story = {
  args: {
    categories: mockCategories,
    onSelect: (categoryId: string) => {
      console.log('Selected category:', categoryId);
    },
  },
};

export const WithInitialValue: Story = {
  args: {
    categories: mockCategories,
    initialValue: 'help',
    onSelect: (categoryId: string) => {
      console.log('Selected category:', categoryId);
    },
  },
};

export const WithCustomIcons: Story = {
  args: {
    categories: [
      {
        id: 'discussions',
        name: 'Discussions',
        icon: '💬',
      },
      {
        id: 'tutorials',
        name: 'Tutorials',
        icon: '📚',
      },
      {
        id: 'code',
        name: 'Code Examples',
        icon: '💻',
      },
      {
        id: 'community',
        name: 'Community',
        icon: '👥',
      },
    ],
    onSelect: (categoryId: string) => {
      console.log('Selected category:', categoryId);
    },
  },
};

export const CustomClassName: Story = {
  args: {
    categories: mockCategories,
    className: 'w-[300px] bg-secondary p-2 rounded-lg',
    onSelect: (categoryId: string) => {
      console.log('Selected category:', categoryId);
    },
  },
};
