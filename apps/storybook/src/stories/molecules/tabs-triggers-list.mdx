import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as TabsTriggersListStories from './tabs-triggers-list.stories';

<Meta of={TabsTriggersListStories} />

# Tabs Triggers List

The Tabs Triggers List component is a flexible navigation component that displays a list of tab triggers either horizontally or vertically.

## Features

- Horizontal and vertical orientations
- Scrollable content
- Icon support
- Responsive design
- Dark mode compatible
- Keyboard navigation

## Usage

```tsx
import { TabsTriggersList } from '@kit/ui/tabs-triggers-list';

export default function Page() {
  return (
    <TabsTriggersList>
      <TabsTrigger value="tab1">Tab 1</TabsTrigger>
      <TabsTrigger value="tab2">Tab 2</TabsTrigger>
    </TabsTriggersList>
  );
}
```

## Examples

### Horizontal

<Canvas of={TabsTriggersListStories.Horizontal} />

### Horizontal with Icons

<Canvas of={TabsTriggersListStories.HorizontalWithIcons} />

### Horizontal with Scroll

<Canvas of={TabsTriggersListStories.HorizontalWithScroll} />

### Vertical

<Canvas of={TabsTriggersListStories.Vertical} />

### Vertical with Icons

<Canvas of={TabsTriggersListStories.VerticalWithIcons} />

### Vertical with Scroll

<Canvas of={TabsTriggersListStories.VerticalWithScroll} />

## Component API

<Controls />

## Accessibility

The Tabs Triggers List component follows accessibility best practices:

- Proper ARIA roles and attributes
- Keyboard navigation support
- Focus management
- High contrast colors
- Screen reader announcements

## Best Practices

1. Use clear, concise tab labels
2. Keep the number of tabs manageable
3. Consider mobile viewports
4. Use icons consistently
5. Test keyboard navigation
6. Ensure proper spacing between tabs
