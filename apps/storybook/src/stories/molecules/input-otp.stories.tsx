import * as React from 'react';

import type { Meta } from '@storybook/nextjs';

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from '@kit/ui/input-otp';

const meta = {
  title: 'Molecules/InputOTP',
  component: InputOTP,
  tags: ['autodocs'],
  argTypes: {
    maxLength: {
      control: 'number',
      description: 'Maximum number of characters',
      defaultValue: 6,
    },
    value: {
      control: 'text',
      description: 'Current value of the OTP input',
    },
    onChange: {
      description: 'Event handler called when the value changes',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    pattern: {
      control: 'text',
      description: 'Pattern for allowed characters',
    },
  },
} satisfies Meta<typeof InputOTP>;

export default meta;

export const Default = {
  render: () => {
    const [value, setValue] = React.useState('');
    return (
      <InputOTP value={value} onChange={setValue} maxLength={6}>
        <InputOTPGroup>
          {Array.from({ length: 6 }, (_, i) => (
            <InputOTPSlot key={i} index={i} />
          ))}
        </InputOTPGroup>
      </InputOTP>
    );
  },
};

export const WithSeparators = {
  render: () => {
    const [value, setValue] = React.useState('');
    return (
      <InputOTP value={value} onChange={setValue} maxLength={6}>
        <InputOTPGroup>
          {Array.from({ length: 6 }, (_, i) => (
            <React.Fragment key={i}>
              <InputOTPSlot index={i} />
              {i !== 5 && <InputOTPSeparator />}
            </React.Fragment>
          ))}
        </InputOTPGroup>
      </InputOTP>
    );
  },
};

export const WithPattern = {
  render: () => {
    const [value, setValue] = React.useState('');
    return (
      <InputOTP value={value} onChange={setValue} maxLength={4} pattern="[0-9]">
        <InputOTPGroup>
          {Array.from({ length: 4 }, (_, i) => (
            <InputOTPSlot key={i} index={i} />
          ))}
        </InputOTPGroup>
      </InputOTP>
    );
  },
};

export const Disabled = {
  render: () => {
    const [value, setValue] = React.useState('123456');
    return (
      <InputOTP value={value} onChange={setValue} maxLength={6} disabled>
        <InputOTPGroup>
          {Array.from({ length: 6 }, (_, i) => (
            <InputOTPSlot key={i} index={i} />
          ))}
        </InputOTPGroup>
      </InputOTP>
    );
  },
};

export const WithVerification = {
  render: () => {
    const [value, setValue] = React.useState('');
    const [isVerifying, setIsVerifying] = React.useState(false);
    const [isValid, setIsValid] = React.useState<boolean | null>(null);

    React.useEffect(() => {
      if (value.length === 6) {
        setIsVerifying(true);
        // Simulate verification
        setTimeout(() => {
          setIsVerifying(false);
          setIsValid(value === '123456');
        }, 1500);
      } else {
        setIsValid(null);
      }
    }, [value]);

    return (
      <div className="space-y-4">
        <InputOTP
          value={value}
          onChange={setValue}
          maxLength={6}
          disabled={isVerifying}
        >
          <InputOTPGroup>
            {Array.from({ length: 6 }, (_, i) => (
              <InputOTPSlot
                key={i}
                index={i}
                className={
                  isValid === true
                    ? 'border-green-500'
                    : isValid === false
                      ? 'border-red-500'
                      : undefined
                }
              />
            ))}
          </InputOTPGroup>
        </InputOTP>
        {isVerifying && (
          <p className="text-muted-foreground text-sm">Verifying code...</p>
        )}
        {isValid === true && (
          <p className="text-sm text-green-500">Code verified successfully!</p>
        )}
        {isValid === false && (
          <p className="text-sm text-red-500">
            Invalid code. Please try again.
          </p>
        )}
      </div>
    );
  },
};
