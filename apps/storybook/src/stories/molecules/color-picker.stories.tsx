'use client';

import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { ColorPicker } from '@kit/ui/color-picker';

const meta = {
  title: 'Molecules/ColorPicker',
  component: ColorPicker,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    pickedColor: {
      control: 'text',
      description: 'The currently selected color value',
    },
    onColorChange: {
      description: 'Callback function when color changes',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof ColorPicker>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    pickedColor: '#3357FF',
    onColorChange: () => {},
  },
  render: () => {
    const [color, setColor] = useState('#3357FF');
    return <ColorPicker pickedColor={color} onColorChange={setColor} />;
  },
};

export const EmptyState: Story = {
  args: {
    pickedColor: '',
    onColorChange: () => {},
  },
  render: () => {
    const [color, setColor] = useState('');
    return <ColorPicker pickedColor={color} onColorChange={setColor} />;
  },
};

export const CustomStyle: Story = {
  args: {
    pickedColor: '#FF338C',
    onColorChange: () => {},
    className: 'w-48 border-2',
  },
  render: () => {
    const [color, setColor] = useState('#FF338C');
    return (
      <ColorPicker
        pickedColor={color}
        onColorChange={setColor}
        className="w-48 border-2"
      />
    );
  },
};

export const WithPreview: Story = {
  args: {
    pickedColor: '#33FFA1',
    onColorChange: () => {},
  },
  render: () => {
    const [color, setColor] = useState('#33FFA1');
    return (
      <div className="space-y-4">
        <ColorPicker pickedColor={color} onColorChange={setColor} />
        <div
          className="h-32 w-full rounded-lg border transition-colors"
          style={{ backgroundColor: color }}
        >
          <div className="flex h-full items-center justify-center font-mono">
            {color}
          </div>
        </div>
      </div>
    );
  },
};

export const MultipleInstances: Story = {
  args: {
    pickedColor: '#FFD700',
    onColorChange: () => {},
  },
  render: () => {
    const [backgroundColor, setBackgroundColor] = useState('#FFD700');
    const [textColor, setTextColor] = useState('#09203f');
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Background Color</label>
          <ColorPicker
            pickedColor={backgroundColor}
            onColorChange={setBackgroundColor}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium">Text Color</label>
          <ColorPicker pickedColor={textColor} onColorChange={setTextColor} />
        </div>
        <div
          className="rounded-lg border p-4 transition-colors"
          style={{ backgroundColor, color: textColor }}
        >
          <h3 className="text-lg font-semibold">Preview</h3>
          <p>Sample text with the selected colors.</p>
        </div>
      </div>
    );
  },
};
