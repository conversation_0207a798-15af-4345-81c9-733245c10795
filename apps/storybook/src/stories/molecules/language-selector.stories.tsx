import type { Meta, StoryObj } from '@storybook/nextjs';
import i18n from 'i18next';
import { I18nextProvider } from 'react-i18next';

import { LanguageSelector } from '@kit/ui/language-selector';

// Initialize i18n for Storybook
i18n.init({
  lng: 'en',
  resources: {
    en: {},
    es: {},
    fr: {},
  },
  supportedLngs: ['en', 'es', 'fr'],
});

const meta = {
  title: 'Molecules/LanguageSelector',
  component: LanguageSelector,
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => (
      <I18nextProvider i18n={i18n}>
        <Story />
      </I18nextProvider>
    ),
  ],
  argTypes: {
    onChange: {
      description: 'Callback function when language changes',
    },
  },
} satisfies Meta<typeof LanguageSelector>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onChange: (locale: string) => {
      console.log('Language changed to:', locale);
    },
  },
};

export const WithCallback: Story = {
  args: {
    onChange: (locale: string) => {
      alert(`Language changed to: ${locale}`);
    },
  },
};

export const InForm: Story = {
  render: () => {
    return (
      <div className="w-[320px] space-y-4 rounded-lg border p-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Language</label>
          <LanguageSelector
            onChange={(locale) => console.log('Language changed to:', locale)}
          />
        </div>
      </div>
    );
  },
};
