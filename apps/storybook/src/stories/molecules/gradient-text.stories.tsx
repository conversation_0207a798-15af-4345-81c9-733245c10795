import type { Meta, StoryObj } from '@storybook/nextjs';

import { GradientText } from '@kit/ui/dojo/atoms/gradient-text';

const meta = {
  title: 'Molecules/Marketing/GradientText',
  component: GradientText,
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
      description: 'Text content to display with gradient effect',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
  },
} satisfies Meta<typeof GradientText>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Gradient Text',
    className: 'from-primary to-secondary',
  },
};

export const SecondaryVariant: Story = {
  args: {
    children: 'Secondary Gradient',
    className: 'from-secondary to-primary',
  },
};

export const AccentVariant: Story = {
  args: {
    children: 'Accent Gradient',
    className: 'from-accent to-secondary',
  },
};

export const CustomStyling: Story = {
  args: {
    children: 'Custom Styled Gradient',
    className: 'from-primary to-secondary text-3xl font-bold tracking-tight',
  },
};
