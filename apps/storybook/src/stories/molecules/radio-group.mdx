import { <PERSON><PERSON>, Meta } from '@storybook/addon-docs/blocks';

import * as RadioGroupStories from './radio-group.stories';

<Meta of={RadioGroupStories} />

# Radio Group

A radio group component that allows users to select a single option from a list of choices.

## Features

- Keyboard navigation support
- Customizable styling
- Support for disabled state
- Required field validation
- Accessible by default
- Custom label components
- Horizontal and vertical layouts

## Usage

```tsx
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';

export default function MyForm() {
  return (
    <RadioGroup defaultValue="option1">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option1" id="option1" />
        <label htmlFor="option1">Option 1</label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option2" id="option2" />
        <label htmlFor="option2">Option 2</label>
      </div>
    </RadioGroup>
  );
}
```

## Props

### RadioGroup Props

- `defaultValue`: The default selected value
- `value`: The controlled value
- `onValueChange`: Callback when value changes
- `disabled`: Whether the radio group is disabled
- `required`: Whether the radio group is required
- `name`: The name of the radio group
- `orientation`: The orientation of the radio group ('horizontal' | 'vertical')
- `className`: Additional CSS classes

### RadioGroupItem Props

- `value`: The value of the radio item
- `id`: The ID of the radio item
- `disabled`: Whether the radio item is disabled
- `required`: Whether the radio item is required
- `className`: Additional CSS classes

### RadioGroupItemLabel Props

- `selected`: Whether the label is selected
- `className`: Additional CSS classes
- `children`: The content of the label

## Accessibility

The Radio Group component follows WAI-ARIA guidelines for radio groups:

- Uses proper ARIA roles (`radiogroup`, `radio`)
- Supports keyboard navigation
- Provides visual feedback for focus states
- Includes proper labeling and descriptions

### Keyboard Interactions

- `Tab`: Moves focus to the radio group
- `Space`: Selects the focused radio item
- `ArrowDown`/`ArrowRight`: Moves focus to the next radio item
- `ArrowUp`/`ArrowLeft`: Moves focus to the previous radio item

## Design Considerations

1. **Visual Hierarchy**

   - Clear distinction between selected and unselected states
   - Consistent spacing between options
   - Visual feedback for hover and focus states

2. **Layout**

   - Vertical layout for longer option text
   - Horizontal layout for simple, short options
   - Consistent alignment with other form elements

3. **States**
   - Default
   - Selected
   - Disabled
   - Focused
   - Hovered

## Best Practices

1. **Labels**

   - Use clear, concise labels
   - Ensure labels are properly associated with radio items
   - Consider using helper text for additional context

2. **Grouping**

   - Group related options together
   - Use appropriate spacing between groups
   - Consider using fieldsets for complex groups

3. **Validation**
   - Clearly indicate required fields
   - Provide clear error messages
   - Use appropriate ARIA attributes for validation states

## Form Integration

The Radio Group component works seamlessly with form libraries and native HTML forms:

```tsx
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';

export default function Form() {
  return (
    <form onSubmit={handleSubmit}>
      <RadioGroup name="subscription" required defaultValue="monthly">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="monthly" id="monthly" />
            <label htmlFor="monthly">Monthly Plan</label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="yearly" id="yearly" />
            <label htmlFor="yearly">Yearly Plan</label>
          </div>
        </div>
      </RadioGroup>
      <button type="submit">Submit</button>
    </form>
  );
}
```

## Customization

The Radio Group component can be customized using Tailwind CSS classes:

```tsx
<RadioGroup className="space-y-4">
  <RadioGroupItemLabel selected className="bg-primary/5 hover:bg-primary/10">
    <RadioGroupItem value="premium" id="premium" />
    <div>
      <label htmlFor="premium" className="text-primary font-semibold">
        Premium
      </label>
      <p className="text-muted-foreground text-sm">
        Enhanced features and support
      </p>
    </div>
  </RadioGroupItemLabel>
</RadioGroup>
```

You can customize:

- Background colors
- Border styles
- Text styles
- Spacing
- Layout
- Hover and focus states
- Transitions and animations
