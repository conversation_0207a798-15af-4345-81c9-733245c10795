'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { FolderPlus, Inbox, Search } from 'lucide-react';

import {
  EmptyState,
  EmptyStateButton,
  EmptyStateHeading,
  EmptyStateText,
} from '@kit/ui/empty-state';

const meta = {
  title: 'Molecules/EmptyState',
  component: EmptyState,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof EmptyState>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    className: 'h-[400px] w-[600px]',
  },
  render: (args) => (
    <EmptyState {...args}>
      <EmptyStateHeading>No items found</EmptyStateHeading>
      <EmptyStateText>Get started by creating your first item.</EmptyStateText>
      <EmptyStateButton>Create Item</EmptyStateButton>
    </EmptyState>
  ),
};

export const WithIcon: Story = {
  args: {
    className: 'h-[400px] w-[600px]',
  },
  render: (args) => (
    <EmptyState {...args}>
      <Inbox className="text-muted-foreground mb-4 h-12 w-12" />
      <EmptyStateHeading>Your inbox is empty</EmptyStateHeading>
      <EmptyStateText>Messages you receive will appear here.</EmptyStateText>
    </EmptyState>
  ),
};

export const NoResults: Story = {
  args: {
    className: 'h-[400px] w-[600px]',
  },
  render: (args) => (
    <EmptyState {...args}>
      <Search className="text-muted-foreground mb-4 h-12 w-12" />
      <EmptyStateHeading>No results found</EmptyStateHeading>
      <EmptyStateText>
        Try adjusting your search terms or filters.
      </EmptyStateText>
      <EmptyStateButton variant="outline">Clear Filters</EmptyStateButton>
    </EmptyState>
  ),
};

export const WithCustomContent: Story = {
  args: {
    className: 'h-[400px] w-[600px]',
  },
  render: (args) => (
    <EmptyState {...args}>
      <div className="bg-primary/10 mb-4 rounded-full p-4">
        <FolderPlus className="text-primary h-8 w-8" />
      </div>
      <EmptyStateHeading>No projects yet</EmptyStateHeading>
      <EmptyStateText>
        Create your first project to get started with collaboration.
      </EmptyStateText>
      <EmptyStateButton>
        <FolderPlus className="mr-2 h-4 w-4" />
        New Project
      </EmptyStateButton>
      <div className="text-muted-foreground mt-4 text-xs">
        Need help? Check our{' '}
        <a href="#" className="underline">
          documentation
        </a>
      </div>
    </EmptyState>
  ),
};

export const Compact: Story = {
  args: {
    className: 'h-[200px] w-[400px]',
  },
  render: (args) => (
    <EmptyState {...args}>
      <EmptyStateHeading className="text-lg">No data</EmptyStateHeading>
      <EmptyStateText className="text-xs">
        Add some data to see it displayed here.
      </EmptyStateText>
      <EmptyStateButton size="sm">Add Data</EmptyStateButton>
    </EmptyState>
  ),
};
