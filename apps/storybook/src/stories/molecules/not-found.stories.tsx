import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import {
  BookX,
  FileQuestion,
  FolderX,
  Home,
  MessagesSquare,
  RefreshCcw,
  SearchX,
  Users2,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { NotFound } from '@kit/ui/dojo/molecules/not-found';

const meta = {
  title: 'Molecules/NotFound',
  component: NotFound,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof NotFound>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default NotFound component with SearchX icon
 */
export const Default: Story = {
  args: {},
};

/**
 * NotFound with a single action button
 */
export const WithSingleAction: Story = {
  args: {
    title: 'Course not found',
    message: 'The course you are looking for has been archived or deleted.',
    icon: BookX,
    actions: (
      <Button variant="default">
        <Home className="mr-2 h-4 w-4" />
        Back to Courses
      </Button>
    ),
  },
};

/**
 * NotFound with multiple actions
 */
export const WithMultipleActions: Story = {
  args: {
    title: 'Forum not found',
    message: 'The forum you are looking for does not exist.',
    icon: MessagesSquare,
    actions: (
      <div className="flex space-x-4">
        <Button variant="outline">
          <Home className="mr-2 h-4 w-4" />
          Home
        </Button>
        <Button variant="default">
          <RefreshCcw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      </div>
    ),
  },
};

/**
 * NotFound for a community with stacked actions
 */
export const WithStackedActions: Story = {
  args: {
    title: 'Community not found',
    message: 'This community may have been deleted or you may not have access.',
    icon: Users2,
    actions: (
      <div className="flex w-full max-w-[300px] flex-col space-y-2">
        <Button variant="default" className="w-full">
          Request Access
        </Button>
        <Button variant="outline" className="w-full">
          Browse Communities
        </Button>
      </div>
    ),
  },
};

/**
 * NotFound with custom icon styling and action
 */
export const CustomIconWithAction: Story = {
  args: {
    title: 'File not found',
    message: 'The requested file could not be found.',
    icon: FileQuestion,
    iconClassName: 'text-primary/50',
    iconSize: 80,
    actions: (
      <Button variant="outline" className="border-dashed">
        Upload New File
      </Button>
    ),
  },
};

/**
 * NotFound with custom background and action
 */
export const CustomStyling: Story = {
  args: {
    title: 'Folder not found',
    message: 'This folder is no longer available.',
    icon: FolderX,
    className: 'bg-muted/50 rounded-lg p-8',
    iconClassName: 'text-warning/50',
    actions: <Button variant="secondary">Create New Folder</Button>,
  },
};

/**
 * NotFound with a very long message and action
 */
export const LongMessage: Story = {
  args: {
    title: 'Resource not found',
    message:
      'We apologize, but the resource you are trying to access is not available. This could be because it has been moved, deleted, or you may not have the necessary permissions to view it. Please check the URL or contact support if you believe this is an error.',
    icon: SearchX,
    actions: (
      <Button variant="link" className="text-primary">
        Contact Support
      </Button>
    ),
  },
};
