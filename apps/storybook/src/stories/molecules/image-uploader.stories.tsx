'use client';

import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { ImageUploader } from '@kit/ui/image-uploader';

const meta = {
  title: 'Molecules/ImageUploader',
  component: ImageUploader,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    value: {
      control: 'text',
      description: 'Current image URL or null',
    },
    imageRounded: {
      control: 'text',
      description: 'Tailwind class for border radius',
    },
    imageSize: {
      control: 'text',
      description: 'Tailwind class for image size',
    },
    onValueChange: {
      description: 'Callback when image is selected or cleared',
    },
  },
} satisfies Meta<typeof ImageUploader>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value: null,
    imageRounded: 'rounded-full',
    imageSize: 'size-20',
    onValueChange: () => {},
  },
  render: (args) => {
    const [image, setImage] = useState<string | null>(args.value ?? null);
    return (
      <ImageUploader
        {...args}
        value={image}
        onValueChange={(file) => {
          if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
              setImage(reader.result as string);
            };
            reader.readAsDataURL(file);
          } else {
            setImage(null);
          }
        }}
      />
    );
  },
};

export const WithImage: Story = {
  args: {
    value: 'https://picsum.photos/200',
    imageRounded: 'rounded-full',
    imageSize: 'size-20',
    onValueChange: () => {},
  },
  render: (args) => {
    const [image, setImage] = useState<string | null>(args.value ?? null);
    return (
      <ImageUploader
        {...args}
        value={image}
        onValueChange={(file) => {
          if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
              setImage(reader.result as string);
            };
            reader.readAsDataURL(file);
          } else {
            setImage(null);
          }
        }}
      />
    );
  },
};

export const CustomSize: Story = {
  args: {
    value: null,
    imageRounded: 'rounded-lg',
    imageSize: 'size-32',
    onValueChange: () => {},
  },
  render: (args) => {
    const [image, setImage] = useState<string | null>(args.value ?? null);
    return (
      <ImageUploader
        {...args}
        value={image}
        onValueChange={(file) => {
          if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
              setImage(reader.result as string);
            };
            reader.readAsDataURL(file);
          } else {
            setImage(null);
          }
        }}
      >
        <div className="text-muted-foreground ml-4 text-sm">
          <p>Upload a square image</p>
          <p>Recommended size: 256x256px</p>
        </div>
      </ImageUploader>
    );
  },
};

export const WithValidation: Story = {
  args: {
    value: null,
    imageRounded: 'rounded-lg',
    imageSize: 'size-24',
    onValueChange: () => {},
  },
  render: (args) => {
    const [image, setImage] = useState<string | null>(args.value ?? null);
    const [error, setError] = useState<string | null>(null);

    const validateImage = (file: File | null) => {
      if (!file) {
        setError(null);
        return true;
      }

      if (!file.type.startsWith('image/')) {
        setError('Please upload an image file');
        return false;
      }

      if (file.size > 5 * 1024 * 1024) {
        setError('Image must be less than 5MB');
        return false;
      }

      setError(null);
      return true;
    };

    return (
      <div className="space-y-2">
        <ImageUploader
          {...args}
          value={image}
          onValueChange={(file) => {
            if (validateImage(file) && file) {
              const reader = new FileReader();
              reader.onloadend = () => {
                setImage(reader.result as string);
              };
              reader.readAsDataURL(file);
            }
          }}
        >
          <div className="text-muted-foreground ml-4 text-sm">
            <p>Max file size: 5MB</p>
            <p>Accepted formats: PNG, JPG, GIF</p>
          </div>
        </ImageUploader>
        {error && <p className="text-destructive text-sm">{error}</p>}
      </div>
    );
  },
};
