import { Can<PERSON>, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as ScrollAreaStories from './scroll-area.stories';

<Meta of={ScrollAreaStories} />

# Scroll Area

A scrollable container that provides a custom scrollbar experience. Built on top of [Radix UI's ScrollArea](https://www.radix-ui.com/docs/primitives/components/scroll-area).

## Features

- Custom scrollbar styling that matches your application's theme
- Smooth scrolling behavior
- Support for both vertical and horizontal scrolling
- Nested scroll areas
- Responsive design
- Keyboard accessible
- Dark mode compatible

## Usage

```tsx
import { ScrollArea } from '@kit/ui/scroll-area';

export default function MyComponent() {
  return (
    <ScrollArea className="h-[200px] w-[350px]">
      {/* Your content here */}
    </ScrollArea>
  );
}
```

## Examples

### Default

A basic scroll area with vertical content.

<Canvas of={ScrollAreaStories.Default} />

### Horizontal Scrolling

A scroll area that enables horizontal scrolling for content that extends beyond the container's width.

<Canvas of={ScrollAreaStories.Horizontal} />

### Nested Scroll Areas

Multiple scroll areas can be nested to create complex scrollable layouts.

<Canvas of={ScrollAreaStories.Nested} />

### Custom Styling

The scroll area can be styled to match your application's design system.

<Canvas of={ScrollAreaStories.CustomStyling} />

## Component API

The ScrollArea component accepts all the properties of a standard HTML div element, plus the following props:

<Controls />

## Accessibility

The ScrollArea component follows WAI-ARIA guidelines for scrollable regions:

- Uses `role="region"` with `aria-label` for the scrollable container
- Keyboard navigation support (arrow keys, Page Up/Down, Home/End)
- Screen reader announcements for scroll position
- Focus management for nested scroll areas

## Design Guidelines

1. **Container Size**

   - Always specify explicit height and width for the scroll area
   - Use relative units (rem, vh) for responsive layouts
   - Consider the content's natural dimensions

2. **Content Structure**

   - Organize content in a clear hierarchy
   - Use appropriate spacing between elements
   - Consider the reading flow direction

3. **Visual Feedback**
   - Ensure scrollbar is visible when needed
   - Provide hover and focus states
   - Maintain contrast for dark mode

## Best Practices

- Always provide explicit dimensions for the scroll area
- Use the appropriate orientation for your content
- Consider mobile and touch device interactions
- Test with different content lengths
- Ensure keyboard navigation works as expected
- Test with screen readers

## Technical Details

### Dependencies

- Radix UI ScrollArea primitive
- Tailwind CSS for styling
- React 18+

### Performance Considerations

- Large content may impact performance
- Consider virtualization for long lists
- Use appropriate CSS containment
- Optimize nested scroll areas

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome for Android)
- IE11 not supported

## Related Components

- `List` - For displaying lists of items
- `Card` - Can be used inside scroll areas
- `Table` - For scrollable tables
- `Dialog` - May contain scrollable content
