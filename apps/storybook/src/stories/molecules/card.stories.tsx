import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';

const meta = {
  title: 'Molecules/Card',
  component: Card,
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Card Content</p>
      </CardContent>
      <CardFooter>
        <p>Card Footer</p>
      </CardFooter>
    </Card>
  ),
};

export const WithActions: Story = {
  render: () => (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Plan</CardTitle>
        <CardDescription>Choose your preferred plan</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p className="text-2xl font-bold">$29/month</p>
          <ul className="list-disc pl-4 text-sm">
            <li>Unlimited access</li>
            <li>24/7 support</li>
            <li>Custom features</li>
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Learn More</Button>
        <Button>Subscribe Now</Button>
      </CardFooter>
    </Card>
  ),
};

export const Interactive: Story = {
  render: () => (
    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
      <CardHeader>
        <CardTitle>Interactive Card</CardTitle>
        <CardDescription>Hover to see the effect</CardDescription>
      </CardHeader>
      <CardContent>
        <p>This card has hover effects and is fully interactive.</p>
      </CardContent>
    </Card>
  ),
};

export const Horizontal: Story = {
  render: () => (
    <Card>
      <div className="flex">
        <div className="bg-muted w-1/3" />
        <div className="w-2/3">
          <CardHeader>
            <CardTitle>Horizontal Layout</CardTitle>
            <CardDescription>Card with side-by-side content</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Content in a horizontal layout</p>
          </CardContent>
        </div>
      </div>
    </Card>
  ),
};

export const Nested: Story = {
  render: () => (
    <Card>
      <CardHeader>
        <CardTitle>Parent Card</CardTitle>
      </CardHeader>
      <CardContent className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Nested Card 1</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Content of nested card 1</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Nested Card 2</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Content of nested card 2</p>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  ),
};

export const CustomStyle: Story = {
  render: () => (
    <Card className="bg-primary text-primary-foreground">
      <CardHeader>
        <CardTitle>Custom Styled Card</CardTitle>
        <CardDescription className="text-primary-foreground/80">
          Card with custom background and text colors
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p>Content with custom styling</p>
      </CardContent>
    </Card>
  ),
};
