'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

const meta = {
  title: 'Molecules/AppBreadcrumbs',
  component: AppBreadcrumbs,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/home/<USER>/123/settings',
      },
    },
  },
  argTypes: {
    values: {
      control: 'object',
      description: 'Custom values for path segments',
    },
    maxDepth: {
      control: 'number',
      description: 'Maximum number of segments to show',
    },
  },
} satisfies Meta<typeof AppBreadcrumbs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    nextjs: {
      navigation: {
        pathname: '/home/<USER>/123/settings',
      },
    },
  },
  render: () => (
    <div className="min-w-[600px]">
      <AppBreadcrumbs />
    </div>
  ),
};

export const WithCustomValues: Story = {
  parameters: {
    nextjs: {
      navigation: {
        pathname: '/home/<USER>/123/settings',
      },
    },
  },
  render: () => (
    <div className="min-w-[600px]">
      <AppBreadcrumbs
        values={{
          '123': 'Acme Corp',
          organizations: 'Communities',
          settings: 'Preferences',
        }}
      />
    </div>
  ),
};

export const WithMaxDepth: Story = {
  parameters: {
    nextjs: {
      navigation: {
        pathname: '/home/<USER>/123/teams/456/members/789/profile',
      },
    },
  },
  render: () => (
    <div className="min-w-[600px]">
      <AppBreadcrumbs
        maxDepth={4}
        values={{
          '123': 'Acme Corp',
          '456': 'Engineering',
          '789': 'John Doe',
        }}
      />
    </div>
  ),
};

export const WithLongPath: Story = {
  parameters: {
    nextjs: {
      navigation: {
        pathname:
          '/home/<USER>/123/projects/456/tasks/789/subtasks/101112/comments/131415',
      },
    },
  },
  render: () => (
    <div className="min-w-[600px]">
      <AppBreadcrumbs
        values={{
          '123': 'Acme Corp',
          '456': 'Website Redesign',
          '789': 'Homepage',
          '101112': 'Navigation Menu',
          '131415': 'Design Feedback',
        }}
      />
    </div>
  ),
};

export const WithDynamicSegments: Story = {
  parameters: {
    nextjs: {
      navigation: {
        pathname: '/home/<USER>/[team]/settings',
        params: {
          organization: '123',
          team: '456',
        },
      },
    },
  },
  render: () => (
    <div className="min-w-[600px]">
      <AppBreadcrumbs
        values={{
          '123': 'Acme Corp',
          '456': 'Engineering Team',
        }}
      />
    </div>
  ),
};
