import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as TopLoadingBarStories from './top-loading-bar.stories';

<Meta of={TopLoadingBarStories} />

# Top Loading Bar

A loading bar indicator that appears at the top of the screen to show loading progress during navigation or data fetching.

## Features

- Continuous loading animation
- Customizable color and height
- Shadow effect for depth
- Automatic progress management
- Clean and minimal design
- Dark theme compatible

## Usage

```tsx
import { TopLoadingBarIndicator } from '@kit/ui/makerkit/top-loading-bar-indicator';

function MyComponent() {
  return (
    <div>
      <TopLoadingBarIndicator />
      {/* Your content */}
    </div>
  );
}
```

## Examples

### Default

Basic usage of the loading bar indicator.

<Canvas of={TopLoadingBarStories.Default} />

### With Simulated Navigation

Example showing the loading bar during page navigation.

<Canvas of={TopLoadingBarStories.WithSimulatedNavigation} />

## Component API

<Controls />

## Guidelines

### Usage Guidelines

1. Placement

   - Always place at the top of the page
   - Keep above all other content
   - Ensure visibility during loading
   - Consider z-index stacking

2. Behavior

   - Show during navigation
   - Display during data fetching
   - Animate smoothly
   - Complete gracefully

3. Performance
   - Minimal impact on page load
   - Efficient animation
   - Clean unmounting
   - Resource cleanup

### Best Practices

1. Implementation

   - Place in layout component
   - Handle cleanup properly
   - Manage state efficiently
   - Consider SSR

2. User Experience

   - Provide visual feedback
   - Keep animations smooth
   - Ensure visibility
   - Maintain consistency

3. Accessibility
   - Support reduced motion
   - Consider color contrast
   - Add ARIA attributes
   - Screen reader friendly
