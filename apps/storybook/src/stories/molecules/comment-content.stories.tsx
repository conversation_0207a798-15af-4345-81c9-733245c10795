import type { Meta, StoryObj } from '@storybook/nextjs';
import { JSONContent } from '@tiptap/react';

import { CommentContent } from '@kit/ui/dojo/molecules/comment-content';

const meta = {
  title: 'Molecules/CommentContent',
  component: CommentContent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CommentContent>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockJsonContent: JSONContent = {
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        { type: 'text', text: 'This is a ' },
        { type: 'text', text: 'rich', marks: [{ type: 'bold' }] },
        { type: 'text', text: ' text comment with ' },
        { type: 'text', text: 'formatting', marks: [{ type: 'italic' }] },
        { type: 'text', text: '.' },
      ],
    },
  ],
};

export const Default: Story = {
  args: {
    content: 'This is a simple text comment.',
  },
};

export const RichTextContent: Story = {
  args: {
    content: mockJsonContent,
  },
};

export const LongContent: Story = {
  args: {
    content:
      'This is a longer comment that spans multiple lines. It contains more text to demonstrate how the component handles longer content. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
  },
};

export const CustomClassName: Story = {
  args: {
    content: 'This is a comment with custom styling.',
    className: 'bg-secondary p-4 rounded-lg',
  },
};
