import React from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { addDays, format, parseISO, subDays } from 'date-fns';
import { Calendar, MessageCircle, User } from 'lucide-react';

import { PostMetadata } from '@kit/ui/dojo/atoms/post-metadata';
import { PostFooter } from '@kit/ui/dojo/molecules/post-footer';
import { ReactionsGroup } from '@kit/ui/dojo/molecules/reactions-group';

const meta = {
  title: 'Molecules/PostFooter',
  component: PostFooter,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PostFooter>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockReactionCounts = {
  '+1': { count: 5, user_reacted: false },
  '-1': { count: 1, user_reacted: false },
  heart: { count: 3, user_reacted: true },
};

const mockReactionTypes = [
  { value: '+1', icon: 'thumbsup' },
  { value: '-1', icon: 'thumbsdown' },
  { value: 'heart', icon: 'heart' },
];

// Helper function to create date metadata
const createDateMetadata = (
  date: Date | string,
  useLongFormat = false,
): {
  icon: React.ReactElement;
  text: string;
  label: string;
} => {
  // Ensure we have a Date object
  const dateObj = typeof date === 'string' ? parseISO(date) : date;

  // Format the date based on the format type
  const formattedDate = useLongFormat
    ? format(dateObj, 'MMMM d, yyyy')
    : format(dateObj, 'yyyy-MM-dd');

  return {
    icon: <Calendar className="text-muted-foreground h-4 w-4" />,
    text: formattedDate,
    label: 'Posted on',
  };
};

export const Default: Story = {
  args: {
    leftContent: (
      <ReactionsGroup
        reactionCounts={mockReactionCounts}
        onReaction={() => {}}
        reactionTypes={mockReactionTypes}
      />
    ),
    rightContent: (
      <PostMetadata
        items={[
          createDateMetadata(new Date()),
          {
            icon: <MessageCircle className="text-muted-foreground h-4 w-4" />,
            text: '5 comments',
            label: 'Number of comments',
          },
        ]}
      />
    ),
  },
};

export const LeftContentOnly: Story = {
  args: {
    leftContent: (
      <ReactionsGroup
        reactionCounts={mockReactionCounts}
        onReaction={() => {}}
        reactionTypes={mockReactionTypes}
      />
    ),
  },
};

export const RightContentOnly: Story = {
  args: {
    rightContent: <PostMetadata items={[createDateMetadata(new Date())]} />,
  },
};

export const CustomStyle: Story = {
  args: {
    leftContent: (
      <ReactionsGroup
        reactionCounts={mockReactionCounts}
        onReaction={() => {}}
        reactionTypes={mockReactionTypes}
      />
    ),
    rightContent: <PostMetadata items={[createDateMetadata(new Date())]} />,
    className: 'bg-muted p-4 rounded-lg',
  },
};

// Additional stories with different date formats and scenarios
export const PastDate: Story = {
  args: {
    rightContent: (
      <PostMetadata
        items={[createDateMetadata(subDays(new Date(), 7), true)]}
      />
    ),
  },
};

export const FutureDate: Story = {
  args: {
    rightContent: (
      <PostMetadata
        items={[createDateMetadata(addDays(new Date(), 7), true)]}
      />
    ),
  },
};

export const RelativeFormat: Story = {
  args: {
    rightContent: (
      <PostMetadata items={[createDateMetadata(new Date(), true)]} />
    ),
  },
};

// New edge case stories
export const VeryLongMetadata: Story = {
  args: {
    rightContent: (
      <PostMetadata
        items={[
          createDateMetadata('2024-02-17T10:30:00.999999Z', true),
          {
            icon: <MessageCircle className="text-muted-foreground h-4 w-4" />,
            text: 'This is a very long comment count that might need truncation or wrapping properly in the UI',
            label:
              'A very long label that tests how the UI handles extended text content in labels',
          },
        ]}
      />
    ),
  },
};

export const HTMLContent: Story = {
  args: {
    rightContent: (
      <PostMetadata
        items={[
          {
            icon: <User className="text-muted-foreground h-4 w-4" />,
            text: '<script>alert("xss")</script>',
            label: 'Testing HTML content handling',
          },
          createDateMetadata('2024-02-17T10:30:00Z'),
        ]}
      />
    ),
  },
};

export const EmojiContent: Story = {
  args: {
    rightContent: (
      <PostMetadata
        items={[
          {
            icon: <User className="text-muted-foreground h-4 w-4" />,
            text: '🎋 Posted by Sensei 🗡️',
            label: '👤 Author',
          },
          createDateMetadata('2024-02-17T10:30:00Z', true),
        ]}
      />
    ),
  },
};
