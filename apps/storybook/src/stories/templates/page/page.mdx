import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as PageStories from './page.stories';

<Meta of={PageStories} />

# Page

The Page component is a versatile layout component that provides different page structures for your application. It supports three main layout styles: sidebar, header, and custom layouts.

## Features

- Multiple layout styles (sidebar, header, custom)
- Responsive design
- Sticky header option
- Flexible content areas
- Dark theme compatible
- Accessible structure

## Usage

The Page component is designed to be used as a wrapper for your page content. It provides a consistent layout structure and handles the arrangement of navigation, headers, and content areas.

<Canvas of={PageStories.DefaultWithSidebar} />

## Layout Styles

### Sidebar Layout (Default)

The default layout style includes a sidebar navigation area and a main content area. This is ideal for dashboard-like interfaces where you need persistent navigation.

```tsx
<Page>
  <PageNavigation>{/* Sidebar content */}</PageNavigation>
  <PageBody>
    <PageHeader title="Title" description="Description" />
    {/* Main content */}
  </PageBody>
</Page>
```

### Header Layout

The header layout style places the navigation at the top of the page. This is suitable for content-focused pages where vertical space is a priority.

<Canvas of={PageStories.WithHeader} />

```tsx
<Page style="header">
  <PageNavigation>{/* Header navigation */}</PageNavigation>
  <PageBody>{/* Page content */}</PageBody>
</Page>
```

### Custom Layout

The custom layout style gives you complete control over the page structure. Use this when you need a unique layout that doesn't fit the predefined patterns.

<Canvas of={PageStories.CustomLayout} />

### Sticky Header

You can make the header stick to the top of the viewport while scrolling by using the `sticky` prop.

<Canvas of={PageStories.StickyHeader} />

## Component API

### Page

The main container component that determines the overall layout structure.

<Controls />

#### Props

- `style`: The layout style to use ('sidebar' | 'header' | 'custom')
- `sticky`: Whether to make the header sticky (boolean)
- `className`: Additional CSS classes
- `contentContainerClassName`: CSS classes for the content container

### PageNavigation

Container for navigation elements (sidebar or header navigation).

```tsx
<PageNavigation>{/* Navigation content */}</PageNavigation>
```

### PageBody

Container for the main content area.

```tsx
<PageBody>{/* Main content */}</PageBody>
```

### PageHeader

Header component for the page title and description.

```tsx
<PageHeader title="Page Title" description="Page description" />
```

#### Props

- `title`: The page title (string | React.ReactNode)
- `description`: The page description (string | React.ReactNode)
- `className`: Additional CSS classes
- `children`: Optional additional content for the header

## Accessibility

The Page component follows accessibility best practices:

- Semantic HTML structure
- Proper heading hierarchy
- Keyboard navigation support
- ARIA attributes for interactive elements

## Design Guidelines

- Use consistent layout styles across similar pages
- Keep navigation items organized and clearly labeled
- Maintain adequate spacing between elements
- Consider mobile responsiveness when adding content
- Use appropriate color contrast for text and backgrounds

## Technical Details

- Built with Tailwind CSS for styling
- Supports dark mode out of the box
- Uses CSS Grid and Flexbox for layouts
- Implements responsive design patterns
- Optimized for performance with minimal DOM nesting

## Related Components

- Sidebar
- Navigation
- Header
- Container
- Layout
