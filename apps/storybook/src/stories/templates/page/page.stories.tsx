import type { <PERSON>a, StoryFn } from '@storybook/nextjs';

import { Page, PageBody, PageHeader, PageNavigation } from '@kit/ui/page';

const meta: Meta<typeof Page> = {
  title: 'Templates/Page',
  component: Page,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;

// Default page with sidebar layout
export const DefaultWithSidebar: StoryFn<typeof Page> = () => (
  <Page>
    <PageNavigation>
      <div className="bg-muted h-screen w-64 p-4">Sidebar Navigation</div>
    </PageNavigation>
    <PageBody>
      <PageHeader title="Dashboard" description="Welcome to your dashboard" />
      <div className="p-4">Main content area</div>
    </PageBody>
  </Page>
);

// Page with header layout
export const WithHeader: StoryFn<typeof Page> = () => (
  <Page style="header">
    <PageNavigation>
      <div className="flex items-center space-x-4">
        <span>Logo</span>
        <span>Menu Item 1</span>
        <span>Menu Item 2</span>
      </div>
    </PageNavigation>
    <PageBody>
      <PageHeader title="Settings" description="Manage your preferences" />
      <div className="p-4">Main content area</div>
    </PageBody>
  </Page>
);

// Custom layout page
export const CustomLayout: StoryFn<typeof Page> = () => (
  <Page style="custom">
    <div className="flex min-h-screen flex-col">
      <header className="bg-muted p-4">Custom Header</header>
      <main className="flex-1 p-4">Custom Content Layout</main>
      <footer className="bg-muted p-4">Custom Footer</footer>
    </div>
  </Page>
);

// Sticky header page
export const StickyHeader: StoryFn<typeof Page> = () => (
  <Page style="header" sticky>
    <PageNavigation>
      <div className="flex items-center space-x-4">
        <span>Logo</span>
        <span>Menu Item 1</span>
        <span>Menu Item 2</span>
      </div>
    </PageNavigation>
    <PageBody>
      <PageHeader title="Long Content" description="Page with sticky header" />
      <div className="space-y-4 p-4">
        {Array.from({ length: 20 }).map((_, i) => (
          <div key={i} className="bg-muted h-24 rounded-lg p-4">
            Scrollable content {i + 1}
          </div>
        ))}
      </div>
    </PageBody>
  </Page>
);
