import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Templates/Coming Soon" />

# Coming Soon

The Coming Soon component is a full-page component designed to create engaging "coming soon" or "under construction" pages. It provides a clean, modern layout with support for a logo, heading, descriptive text, and call-to-action button.

## Features

- Full-screen layout
- Centered content
- Logo placement
- Customizable heading with gradient support
- Secondary text with gradient styling
- Call-to-action button
- Responsive design
- Theme-aware styling

## Usage

```tsx
import {
  ComingSoon,
  ComingSoonButton,
  ComingSoonHeading,
  ComingSoonLogo,
  ComingSoonText,
} from '@kit/ui/coming-soon';

export function MyComingSoonPage() {
  return (
    <ComingSoon>
      <ComingSoonLogo>
        <img src="/logo.svg" alt="Company Logo" />
      </ComingSoonLogo>

      <ComingSoonHeading>Something Amazing is Coming Soon</ComingSoonHeading>

      <ComingSoonText>
        We're working hard to bring you our new feature. Stay tuned for updates!
      </ComingSoonText>

      <ComingSoonButton>Notify Me</ComingSoonButton>
    </ComingSoon>
  );
}
```

## Components

### ComingSoon

The main container component that provides the layout structure.

**Props:**

- `className?: string` - Additional CSS classes
- `children: React.ReactNode` - Component children
- All HTML div attributes

### ComingSoonLogo

A component for displaying a logo in the top-left corner.

**Props:**

- `className?: string` - Additional CSS classes
- All HTML img attributes

### ComingSoonHeading

The main heading component with gradient styling.

**Props:**

- `className?: string` - Additional CSS classes
- All HTML heading attributes

### ComingSoonText

A component for descriptive text with gradient secondary styling.

**Props:**

- `className?: string` - Additional CSS classes
- All HTML paragraph attributes

### ComingSoonButton

A call-to-action button component.

**Props:**

- `className?: string` - Additional CSS classes
- All Button component props

## Styling

The component uses Tailwind CSS for styling and follows the application's theme:

```tsx
<div className="container flex min-h-screen flex-col items-center justify-center space-y-12 p-4">
  {/* Logo */}
  <div className="fixed left-8 top-8">...</div>

  {/* Content Container */}
  <div className="mx-auto flex w-full max-w-4xl flex-col items-center justify-center space-y-8 text-center">
    {/* Heading */}
    <h1>...</h1>

    {/* Text */}
    <div className="mx-auto max-w-2xl">
      <p className="text-muted-foreground text-lg md:text-xl">...</p>
    </div>

    {/* Button */}
    <button className="mt-8">...</button>
  </div>
</div>
```

## Best Practices

1. Keep the heading concise and engaging
2. Provide clear, informative text about what's coming
3. Include a call-to-action when possible
4. Use high-quality logos and images
5. Ensure the page is responsive across all device sizes

## Examples

### Basic Usage

```tsx
<ComingSoon>
  <ComingSoonHeading>Coming Soon</ComingSoonHeading>
  <ComingSoonText>We're working on something exciting!</ComingSoonText>
</ComingSoon>
```

### With Logo and CTA

```tsx
<ComingSoon>
  <ComingSoonLogo>
    <img src="/logo.svg" alt="Logo" />
  </ComingSoonLogo>
  <ComingSoonHeading>New Features Coming Soon</ComingSoonHeading>
  <ComingSoonText>
    We're adding exciting new features to enhance your experience.
  </ComingSoonText>
  <ComingSoonButton>Join Waitlist</ComingSoonButton>
</ComingSoon>
```

### With Custom Styling

```tsx
<ComingSoon className="from-background to-secondary/20 bg-linear-to-b">
  <ComingSoonHeading className="text-4xl font-bold">
    Launch Coming Soon
  </ComingSoonHeading>
  <ComingSoonText className="text-xl">
    Get ready for something extraordinary.
  </ComingSoonText>
  <ComingSoonButton className="bg-primary hover:bg-primary/90">
    Get Notified
  </ComingSoonButton>
</ComingSoon>
```
