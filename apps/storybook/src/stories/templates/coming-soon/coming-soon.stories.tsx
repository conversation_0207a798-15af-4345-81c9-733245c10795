import Image from 'next/image';

import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import {
  ComingSoon,
  ComingSoonButton,
  ComingSoonHeading,
  ComingSoonLogo,
  ComingSoonText,
} from '@kit/ui/dojo/organisms/coming-soon';

const meta = {
  title: 'Templates/Coming Soon',
  component: ComingSoon,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ComingSoon>;

export default meta;
type Story = StoryObj<typeof ComingSoon>;

export const Basic: Story = {
  args: {
    children: (
      <>
        <ComingSoonHeading>Coming Soon</ComingSoonHeading>
        <ComingSoonText>
          We&apos;re working on something exciting!
        </ComingSoonText>
      </>
    ),
  },
};

export const WithLogoAndCTA: Story = {
  args: {
    children: (
      <>
        <ComingSoonLogo>
          <Image
            src="https://i.pravatar.cc/200"
            alt="Logo"
            width={48}
            height={48}
            className="h-12 w-12"
          />
        </ComingSoonLogo>
        <ComingSoonHeading>New Features Coming Soon</ComingSoonHeading>
        <ComingSoonText>
          We&apos;re adding exciting new features to enhance your experience.
        </ComingSoonText>
        <ComingSoonButton>Join Waitlist</ComingSoonButton>
      </>
    ),
  },
};

export const CustomStyling: Story = {
  args: {
    className: 'from-background to-secondary/20 bg-linear-to-b',
    children: (
      <>
        <ComingSoonHeading className="text-4xl font-bold">
          Launch Coming Soon
        </ComingSoonHeading>
        <ComingSoonText className="text-xl">
          Get ready for something extraordinary.
        </ComingSoonText>
        <ComingSoonButton className="bg-primary hover:bg-primary/90">
          Get Notified
        </ComingSoonButton>
      </>
    ),
  },
};

export const WithCustomContent: Story = {
  args: {
    children: (
      <>
        <ComingSoonLogo>
          <Image
            src="https://via.placeholder.com/150"
            alt="Logo"
            width={64}
            height={64}
            className="h-16 w-16"
          />
        </ComingSoonLogo>
        <ComingSoonHeading>
          Our New Platform Launches in
          <div className="text-primary mt-4">7 Days</div>
        </ComingSoonHeading>
        <ComingSoonText>
          <div className="space-y-4">
            <p>
              We&apos;re putting the finishing touches on something amazing.
              Sign up now to be the first to know when we launch!
            </p>
            <p className="text-muted-foreground text-sm">
              Early birds get exclusive access to premium features.
            </p>
          </div>
        </ComingSoonText>
        <ComingSoonButton>
          <div className="flex items-center gap-2">
            <span>Join Early Access</span>
            <span>→</span>
          </div>
        </ComingSoonButton>
      </>
    ),
  },
};
