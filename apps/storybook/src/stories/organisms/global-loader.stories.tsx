import type { Meta, StoryObj } from '@storybook/nextjs';

import { GlobalLoader } from '@kit/ui/global-loader';

const meta = {
  title: 'Organisms/GlobalLoader',
  component: GlobalLoader,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    displayLogo: {
      control: 'boolean',
      description: 'Whether to display the logo in the loading overlay',
    },
    fullPage: {
      control: 'boolean',
      description: 'Whether to display the loader in full page mode',
    },
    displaySpinner: {
      control: 'boolean',
      description: 'Whether to display the loading spinner',
    },
    displayTopLoadingBar: {
      control: 'boolean',
      description: 'Whether to display the top loading bar',
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof GlobalLoader>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default loader with all features enabled
export const Default: Story = {
  args: {
    displayLogo: true,
    fullPage: true,
    displaySpinner: true,
    displayTopLoadingBar: true,
  },
};

// Loader with only spinner
export const SpinnerOnly: Story = {
  args: {
    displayLogo: false,
    fullPage: false,
    displaySpinner: true,
    displayTopLoadingBar: false,
  },
};

// Loader with only top loading bar
export const TopLoadingBarOnly: Story = {
  args: {
    displayLogo: false,
    fullPage: false,
    displaySpinner: false,
    displayTopLoadingBar: true,
  },
};

// Loader with custom content
export const WithCustomContent: Story = {
  args: {
    displayLogo: true,
    fullPage: true,
    displaySpinner: true,
    displayTopLoadingBar: true,
  },
  render: (args) => (
    <GlobalLoader {...args}>
      <div className="text-muted-foreground text-center">
        Loading your content...
      </div>
    </GlobalLoader>
  ),
};
