import type { Meta, StoryObj } from '@storybook/nextjs';

import { ForumPostsDataListSkeleton } from '@kit/ui/dojo/organisms/forum-posts-data-list-skeleton';

const meta = {
  title: 'Organisms/ForumPostsDataListSkeleton',
  component: ForumPostsDataListSkeleton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ForumPostsDataListSkeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
