import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as CalendarStories from './calendar.stories';

<Meta of={CalendarStories} />

# Calendar

A date picker calendar component built on top of react-day-picker. Supports single date, date range, and multiple date selection modes with a beautiful and accessible interface.

## Features

- Single date selection
- Date range selection
- Multiple dates selection
- Customizable styling
- Disabled dates support
- Outside days display
- Keyboard navigation
- Screen reader support
- Dark theme compatible
- Responsive design

## Usage

```tsx
import { useState } from 'react';

import { Calendar } from '@kit/ui/calendar';

function MyCalendar() {
  const [date, setDate] = useState<Date>();

  return (
    <Calendar
      mode="single"
      selected={date}
      onSelect={setDate}
      className="rounded-md border"
    />
  );
}
```

## Examples

### Default Calendar

Basic calendar with single date selection.

<Canvas of={CalendarStories.Default} />

### Date Range Selection

Calendar configured for selecting a date range.

<Canvas of={CalendarStories.Range} />

### Multiple Dates Selection

Calendar that allows selecting multiple dates.

<Canvas of={CalendarStories.Multiple} />

### With Disabled Dates

Calendar with specific dates disabled.

<Canvas of={CalendarStories.WithDisabledDates} />

### With Footer

Calendar with a custom footer element.

<Canvas of={CalendarStories.WithFooter} />

### With Custom Styles

Calendar with custom styling applied.

<Canvas of={CalendarStories.WithCustomStyles} />

## Component API

<Controls />

## Props

The Calendar component accepts all props from react-day-picker's DayPicker component, plus:

- `className`: string - Additional CSS classes
- `classNames`: object - Custom class names for various elements
- `showOutsideDays`: boolean - Whether to show days from previous/next months

## Styling

The Calendar uses CSS classes for styling different parts:

- `months`: Container for months
- `month`: Individual month container
- `caption`: Month and year header
- `caption_label`: Month and year text
- `nav`: Navigation container
- `nav_button`: Navigation buttons
- `table`: Calendar grid
- `head_row`: Week day names row
- `head_cell`: Week day name cell
- `row`: Week row
- `cell`: Day cell
- `day`: Day button
- `day_selected`: Selected day
- `day_today`: Today's date
- `day_outside`: Days from previous/next months
- `day_disabled`: Disabled days
- `day_range_middle`: Days within selected range
- `day_hidden`: Hidden days

## Accessibility

The Calendar component follows WAI-ARIA guidelines:

### Keyboard Navigation

- `Space/Enter`: Select focused date
- `Arrow keys`: Move focus between days
- `Home/End`: Move to start/end of week
- `PageUp/PageDown`: Move to previous/next month
- `Alt + PageUp/PageDown`: Move to previous/next year

### ARIA Attributes

- Uses semantic table structure
- Provides proper labeling
- Announces selected dates
- Indicates current date
- Manages focus appropriately

## Guidelines

### Usage Guidelines

1. Selection Modes

   - Use single mode for simple date picking
   - Use range mode for period selection
   - Use multiple mode sparingly

2. Visual Design

   - Maintain consistent spacing
   - Use clear visual hierarchy
   - Consider mobile viewports
   - Support dark theme

3. Interaction Design
   - Provide clear feedback
   - Handle edge cases
   - Consider touch interfaces
   - Support keyboard users

### Best Practices

1. Implementation

   - Initialize with current date
   - Handle null/undefined states
   - Validate date ranges
   - Consider timezone handling

2. Accessibility

   - Maintain keyboard support
   - Provide clear announcements
   - Support screen readers
   - Test with assistive tech

3. Performance
   - Memoize callbacks
   - Optimize re-renders
   - Handle large date ranges
   - Consider bundle size
