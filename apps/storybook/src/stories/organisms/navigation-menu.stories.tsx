'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Bell, LogOut, Settings, User, UserCircle } from 'lucide-react';

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@kit/ui/navigation-menu';

const meta = {
  title: 'Organisms/NavigationMenu',
  component: NavigationMenu,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof NavigationMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuLink className={navigationMenuTriggerStyle()} href="/">
            Home
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink
            className={navigationMenuTriggerStyle()}
            href="/about"
          >
            About
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuLink
            className={navigationMenuTriggerStyle()}
            href="/contact"
          >
            Contact
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
};

export const WithDropdowns: Story = {
  parameters: {
    layout: 'centered',
    docs: {
      canvas: {
        height: 400,
      },
    },
  },
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Getting Started</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-6">
              <li className="row-span-3">
                <NavigationMenuLink asChild>
                  <div
                    className="from-muted/50 to-muted bg-linear-to-b outline-hidden flex h-full w-full select-none flex-col justify-end rounded-md p-6 no-underline focus:shadow-md"
                    role="link"
                    aria-label="Documentation"
                  >
                    <div className="mb-2 mt-4 text-lg font-medium">
                      Documentation
                    </div>
                    <p className="text-muted-foreground text-sm leading-tight">
                      Learn how to integrate our tools and explore the API.
                    </p>
                  </div>
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  href="/quickstart"
                  className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground outline-hidden block select-none space-y-1 rounded-md p-3 leading-none no-underline transition-colors"
                >
                  <div className="text-sm font-medium leading-none">
                    Quickstart
                  </div>
                  <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">
                    Get up and running with our platform in minutes.
                  </p>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Components</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2">
              <li>
                <NavigationMenuLink
                  href="/components/button"
                  className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground outline-hidden block select-none space-y-1 rounded-md p-3 leading-none no-underline transition-colors"
                >
                  <div className="text-sm font-medium leading-none">Button</div>
                  <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">
                    Clickable elements for user interaction.
                  </p>
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  href="/components/dialog"
                  className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground outline-hidden block select-none space-y-1 rounded-md p-3 leading-none no-underline transition-colors"
                >
                  <div className="text-sm font-medium leading-none">Dialog</div>
                  <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">
                    Modal dialogs and popovers.
                  </p>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
};

export const WithIcons: Story = {
  parameters: {
    layout: 'centered',
    docs: {
      canvas: {
        height: 300,
      },
    },
  },
  render: () => (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger>
            <User className="h-4 w-4" />
            <span className="ml-2">Settings</span>
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[200px] gap-2 p-4">
              <li>
                <NavigationMenuLink
                  className="hover:bg-accent flex items-center space-x-2 rounded-md p-2"
                  href="#"
                >
                  <UserCircle className="h-4 w-4" />
                  <span>Profile</span>
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  className="hover:bg-accent flex items-center space-x-2 rounded-md p-2"
                  href="#"
                >
                  <Bell className="h-4 w-4" />
                  <span>Notifications</span>
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  className="hover:bg-accent flex items-center space-x-2 rounded-md p-2"
                  href="#"
                >
                  <Settings className="h-4 w-4" />
                  <span>Preferences</span>
                </NavigationMenuLink>
              </li>
              <li>
                <NavigationMenuLink
                  className="hover:bg-accent text-destructive flex items-center space-x-2 rounded-md p-2"
                  href="#"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign out</span>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  ),
};
