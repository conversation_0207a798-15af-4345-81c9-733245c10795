import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as LoadingOverlayStories from './loading-overlay.stories';

<Meta of={LoadingOverlayStories} />

# Loading Overlay

A flexible loading overlay component that displays a spinner with optional text content. It can be used to indicate loading states either for the full page or specific sections of your application.

## Features

- Full-page or inline loading overlay
- Customizable spinner appearance
- Optional text content
- Dark mode compatible
- Accessible loading indicator
- Flexible positioning and styling

## Usage

```tsx
import { LoadingOverlay } from '@kit/ui/loading-overlay';

// Full page loading
function LoadingPage() {
  return <LoadingOverlay>Loading your content...</LoadingOverlay>;
}

// Section loading
function LoadingSection() {
  return (
    <div className="relative">
      <LoadingOverlay fullPage={false}>Processing...</LoadingOverlay>
    </div>
  );
}
```

## Examples

### Default Full Page

The default loading overlay that covers the entire viewport.

<Canvas of={LoadingOverlayStories.Default} />

### Inline Loading

A loading overlay that can be used within a container.

<Canvas of={LoadingOverlayStories.Inline} />

### Custom Styling

Customized spinner and container styling.

<Canvas of={LoadingOverlayStories.CustomSpinner} />

### Spinner Only

A minimal loading overlay without text content.

<Canvas of={LoadingOverlayStories.SpinnerOnly} />

## Component API

### Props

<Controls />

| Prop             | Type      | Default | Description                                  |
| ---------------- | --------- | ------- | -------------------------------------------- |
| children         | ReactNode | -       | Optional text content below the spinner      |
| className        | string    | -       | Additional classes for the container         |
| spinnerClassName | string    | -       | Additional classes for the spinner component |
| fullPage         | boolean   | true    | Whether to display as a full-page overlay    |

## Technical Details

### Implementation Notes

- Uses the Spinner component for the loading indicator
- Implements flexible positioning with CSS Grid and Flexbox
- Supports both full-page and contained overlays
- Maintains proper z-index stacking
- Handles dark mode through Tailwind CSS classes

### Accessibility

The Loading Overlay follows accessibility best practices:

- Uses semantic HTML structure
- Includes proper ARIA roles and attributes
- Maintains focus management
- Provides clear loading state indication
- Supports screen readers

## Design Guidelines

1. **Usage**

   - Use for loading states that block user interaction
   - Consider the context when choosing between full-page and inline
   - Keep loading messages concise and informative
   - Ensure sufficient contrast for visibility

2. **Styling**
   - Maintain consistent branding
   - Use appropriate spacing and sizing
   - Consider the visual hierarchy
   - Ensure dark mode compatibility

## Best Practices

1. **Implementation**

   - Place at appropriate DOM level
   - Consider loading state duration
   - Handle loading timeouts
   - Provide meaningful loading messages

2. **User Experience**
   - Use for operations longer than 300ms
   - Avoid sudden layout shifts
   - Maintain visual consistency
   - Consider progressive loading patterns

## Related Components

- Spinner (`@kit/ui/spinner`)
- GlobalLoader (`@kit/ui/global-loader`)
- If (`@kit/ui/if`)
