import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Lock } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { CourseCard } from '@kit/ui/dojo/organisms/course-card';
import type { Course, Seconds } from '@kit/ui/dojo/types';

// Define createSeconds function since it's not exported from @kit/ui/dojo/types
function createSeconds(n: number): Seconds {
  if (n < 0) throw new Error('Seconds cannot be negative');
  return n as Seconds;
}

const meta = {
  title: 'Course/Organisms/CourseCard',
  component: CourseCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseCard>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultCourse: Course = {
  id: '1',
  slug: 'react-fundamentals',
  title: 'React Fundamentals',
  description: 'Learn the basics of React development',
  coverUrl: 'https://picsum.photos/400/300',
  status: 'published',
  lessons: 12,
  durationSeconds: createSeconds(7200),
  studentStatus: 'not_enrolled',
  progressSeconds: createSeconds(0),
  prerequisites: [
    { id: '1', title: 'JavaScript Basics', completed: true },
    { id: '2', title: 'HTML & CSS', completed: false },
  ],
};

export const Default: Story = {
  args: {
    course: defaultCourse,
    onCardClick: () => alert('Card clicked'),
    formatDuration: (seconds) => `${Math.floor(seconds / 60)} min`,
  },
};

export const WithHeaderActions: Story = {
  args: {
    ...Default.args,
    headerActions: <Button variant="ghost">Edit</Button>,
  },
};

export const WithFooterActions: Story = {
  args: {
    ...Default.args,
    footerActions: <Button>Enroll Now</Button>,
  },
};

export const WithOverlay: Story = {
  args: {
    ...Default.args,
    overlay: {
      title: 'Private Course',
      description: 'This course is private',
      icon: <Lock className="mb-2 h-8 w-8" />,
      type: 'private',
    },
  },
};

export const Enrolled: Story = {
  args: {
    ...Default.args,
    course: {
      ...defaultCourse,
      studentStatus: 'enrolled',
      progressSeconds: createSeconds(3600),
    },
  },
};

export const Completed: Story = {
  args: {
    ...Default.args,
    course: {
      ...defaultCourse,
      studentStatus: 'completed',
    },
  },
};

export const Loading: Story = {
  args: {
    ...Default.args,
    isLoading: true,
  },
};
