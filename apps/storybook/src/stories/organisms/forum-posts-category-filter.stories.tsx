import type { Meta, StoryObj } from '@storybook/nextjs';

import { CategoryFilter } from '@kit/ui/dojo/organisms/category-filter';

const meta = {
  title: 'Organisms/CategoryFilter',
  component: CategoryFilter,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CategoryFilter>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockCategories = [
  {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'General',
    description: 'General discussion topics',
    icon: '💬',
    slug: 'general',
  },
  {
    id: '123e4567-e89b-12d3-a456-426614174001',
    name: 'Help',
    description: 'Get help with issues',
    icon: '❓',
    slug: 'help',
  },
  {
    id: '123e4567-e89b-12d3-a456-426614174002',
    name: 'Feedback',
    description: 'Share your feedback',
    icon: '📝',
    slug: 'feedback',
  },
];

export const Default: Story = {
  args: {
    categories: mockCategories,
    initialCategoriesShown: 2,
    minCategoriesForShowMore: 2,
    defaultCategory: 'all',
  },
};

export const SingleCategory: Story = {
  args: {
    categories: mockCategories.slice(0, 1),
    initialCategoriesShown: 1,
    minCategoriesForShowMore: 1,
    defaultCategory: 'all',
  },
};

export const NoCategories: Story = {
  args: {
    categories: [],
    initialCategoriesShown: 2,
    minCategoriesForShowMore: 2,
    defaultCategory: 'all',
  },
};
