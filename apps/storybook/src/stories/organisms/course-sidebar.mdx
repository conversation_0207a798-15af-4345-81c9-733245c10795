import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/addon-docs/blocks';

import * as CourseSidebarStories from './course-sidebar.stories';

<Meta of={CourseSidebarStories} />

# Course Sidebar

The Course Sidebar is a specialized implementation of the ShadCN Sidebar component, tailored for displaying course content organized into chapters and lessons. It provides a structured navigation interface for users to browse through course materials.

## Features

- **Hierarchical Structure**: Organizes content into chapters and standalone lessons
- **Visual Indicators**: Shows content type (video, exercise, etc.) and status (published, draft)
- **Expandable Chapters**: Allows collapsing/expanding chapters to focus on specific sections
- **State Management**: Tracks currently selected lesson and expanded chapters
- **Permission-based UI**: Adapts interface based on user permissions (edit/view-only)
- **Responsive Design**: Supports both expanded and collapsed states

## Installation

The Course Sidebar relies on the ShadCN Sidebar component. Make sure you have the required dependencies:

```bash
# If you don't have the shadcn-sidebar components yet
npm install @shadcn/ui @radix-ui/react-collapsible
```

## Usage

Here's a basic implementation of the Course Sidebar:

```tsx
import { SidebarProvider } from '@kit/ui/shadcn-sidebar';

import { CourseSidebar } from './course-sidebar';

export function CourseLayout() {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <CourseSidebar
          chapters={chapters}
          lessons={lessons}
          currentLessonId={currentLessonId}
          onLessonSelect={handleLessonSelect}
          hasEditPermission={true}
        />
        <div className="flex-1">{/* Course content goes here */}</div>
      </div>
    </SidebarProvider>
  );
}
```

## Examples

### Default Sidebar

The default course sidebar showing chapters and lessons with full permissions.

<Canvas of={CourseSidebarStories.Default} />

### Collapsed Sidebar

The sidebar in its collapsed state, showing only icons.

<Canvas of={CourseSidebarStories.Collapsed} />

### Without Edit Permissions

View-only mode of the sidebar with no edit capabilities.

<Canvas of={CourseSidebarStories.WithoutPermissions} />

## Component API

### CourseSidebar

| Prop                | Type                         | Default     | Description                                        |
| ------------------- | ---------------------------- | ----------- | -------------------------------------------------- |
| `chapters`          | `Chapter[]`                  | `[]`        | Array of chapter objects                           |
| `lessons`           | `Lesson[]`                   | `[]`        | Array of standalone lesson objects                 |
| `currentLessonId`   | `string`                     | `undefined` | ID of the currently selected lesson                |
| `onLessonSelect`    | `(lessonId: string) => void` | `undefined` | Callback function when a lesson is selected        |
| `hasEditPermission` | `boolean`                    | `false`     | Whether the user has permission to edit the course |

### Chapter

| Property         | Type       | Description                        |
| ---------------- | ---------- | ---------------------------------- |
| `id`             | `string`   | Unique identifier for the chapter  |
| `title`          | `string`   | Title of the chapter               |
| `sequence_order` | `number`   | Order of the chapter in the course |
| `lessons`        | `Lesson[]` | Array of lessons in this chapter   |

### Lesson

| Property         | Type                                        | Description                                      |
| ---------------- | ------------------------------------------- | ------------------------------------------------ |
| `id`             | `string`                                    | Unique identifier for the lesson                 |
| `title`          | `string`                                    | Title of the lesson                              |
| `status`         | `'draft' \| 'published'`                    | Publication status of the lesson                 |
| `sequence_order` | `number`                                    | Order of the lesson within its chapter or course |
| `chapter_id`     | `string \| null`                            | ID of the parent chapter or null if standalone   |
| `content_type`   | `'video' \| 'quiz' \| 'exercise' \| 'text'` | Type of content in the lesson                    |

## Accessibility

- Chapter headers can be navigated using keyboard
- Expanded state is tracked for proper screen reader announcement
- Color contrast meets WCAG AA standards for text readability
- Focus indicators are visible for keyboard navigation

## Design Guidelines

- Use consistent spacing and indentation for hierarchy
- Ensure clear visual differentiation between chapters and lessons
- Provide strong visual indicators for the current selection
- When in collapsed mode, ensure icons are intuitive and recognizable

## Browser Support

The component is compatible with all modern browsers including:

- Chrome/Edge (latest)
- Firefox (latest)
- Safari (latest)

## Dependencies

- `@kit/ui/shadcn-sidebar` - Base sidebar components
- `@kit/ui/badge` - For status indicators
- `@kit/ui/dropdown-menu` - For action menus
