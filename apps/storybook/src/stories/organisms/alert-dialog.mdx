import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as AlertDialogStories from './alert-dialog.stories';

<Meta of={AlertDialogStories} />

# Alert Dialog

A modal dialog that interrupts the user with important content and expects a response. Built on top of Radix UI's Alert Dialog primitive, it provides a robust foundation for building accessible alert dialogs.

## Features

- Focus management
- Keyboard navigation
- Screen reader announcements
- Customizable styling
- Responsive design
- Dark theme compatible
- WAI-ARIA compliant
- Animated transitions

## Usage

```tsx
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';

function MyAlertDialog() {
  return (
    <AlertDialog>
      <AlertDialogTrigger>Open</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction>Continue</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
```

## Examples

### Default

Basic alert dialog with a confirmation action.

<Canvas of={AlertDialogStories.Default} />

### Destructive Action

Alert dialog for destructive actions like deletion.

<Canvas of={AlertDialogStories.Destructive} />

### Warning

Alert dialog for warning users about potential data loss.

<Canvas of={AlertDialogStories.Warning} />

### With Custom Content

Alert dialog with additional content and custom styling.

<Canvas of={AlertDialogStories.WithCustomContent} />

## Component API

<Controls />

## Subcomponents

### AlertDialog

The root component that manages the dialog state.

### AlertDialogTrigger

The button that opens the dialog.

Props:

- `asChild`: boolean - Whether to merge props onto child element

### AlertDialogContent

The container for dialog content.

Props:

- `className`: string - Additional CSS classes
- `forceMount`: boolean - Forces mounting when true

### AlertDialogHeader

Container for the dialog title and description.

### AlertDialogFooter

Container for dialog actions.

### AlertDialogTitle

The title of the dialog.

Props:

- `className`: string - Additional CSS classes

### AlertDialogDescription

The description text of the dialog.

Props:

- `className`: string - Additional CSS classes

### AlertDialogAction

The confirmation button.

Props:

- `className`: string - Additional CSS classes

### AlertDialogCancel

The cancel button.

Props:

- `className`: string - Additional CSS classes

## Accessibility

The Alert Dialog follows WAI-ARIA guidelines for modal dialogs:

### Keyboard Navigation

- `Space/Enter`: Open dialog (trigger)
- `Space/Enter`: Confirm action (action button)
- `Escape`: Close dialog
- `Tab`: Move focus between focusable elements
- `Shift + Tab`: Move focus to previous element

### ARIA Attributes

- Uses `role="alertdialog"`
- Manages `aria-labelledby`
- Sets `aria-describedby`
- Handles `aria-modal`
- Controls focus management

## Guidelines

### Usage Guidelines

1. Content Structure

   - Use clear, concise titles
   - Provide helpful descriptions
   - Keep actions obvious
   - Consider content hierarchy

2. Visual Design

   - Maintain consistent spacing
   - Use appropriate colors
   - Consider motion
   - Support dark theme

3. Interaction Design
   - Handle focus states
   - Provide clear feedback
   - Consider animations
   - Support touch devices

### Best Practices

1. When to Use

   - Destructive actions
   - Important confirmations
   - Data loss prevention
   - Critical warnings

2. Content Writing

   - Be clear and direct
   - Explain consequences
   - Provide solutions
   - Use appropriate tone

3. Implementation
   - Handle focus properly
   - Manage keyboard input
   - Consider mobile use
   - Test accessibility
