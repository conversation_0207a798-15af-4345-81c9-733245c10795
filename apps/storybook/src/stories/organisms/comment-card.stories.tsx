import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { JSONContent } from '@tiptap/react';
import { Heart, Reply, Star, ThumbsUp } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Button, buttonVariants } from '@kit/ui/button';
import { CommentCard } from '@kit/ui/dojo/organisms/comment-card';
import { cn } from '@kit/ui/utils';

const meta = {
  title: 'Organisms/CommentCard',
  component: CommentCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CommentCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to create Avatar (copied)
const createAvatar = (args: {
  authorFirstName: string;
  authorLastName: string;
  authorPictureUrl?: string;
}) => (
  <div className="flex items-center gap-2">
    <Avatar>
      {args.authorPictureUrl && <AvatarImage src={args.authorPictureUrl} />}
      <AvatarFallback>
        {args.authorFirstName?.charAt(0)}
        {args.authorLastName?.charAt(0)}
      </AvatarFallback>
    </Avatar>
    <span className="font-semibold">
      {args.authorFirstName} {args.authorLastName}
    </span>
  </div>
);

// Mock components for reactions and actions
const mockReactions = (
  <div className="flex gap-2">
    <Button variant="ghost" size="sm" className="flex items-center gap-1">
      <ThumbsUp className="size-4" />
      <span>12</span>
    </Button>
    <Button variant="ghost" size="sm" className="flex items-center gap-1">
      <Heart className="size-4" />
      <span>5</span>
    </Button>
    <Button variant="ghost" size="sm" className="flex items-center gap-1">
      <Star className="size-4" />
      <span>3</span>
    </Button>
  </div>
);

const mockHeaderActions = (
  <div className="flex">
    <Button
      variant="ghost"
      size="icon"
      className={cn(buttonVariants({ variant: 'ghost', size: 'icon' }))}
    >
      <Reply className="size-4" />
    </Button>
  </div>
);

// Mock content for comments
const mockContent: JSONContent = {
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: 'This is a sample comment with some content to show how the comment card looks with text.',
        },
      ],
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: 'It includes multiple paragraphs to demonstrate the layout with more content.',
        },
      ],
    },
  ],
};

export const Default: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'John',
      authorLastName: 'Doe',
      authorPictureUrl: 'https://i.pravatar.cc/300',
    }),
    timestamp: '2024-02-17T10:30:00Z',
    content: mockContent,
    reactions: mockReactions,
  },
};

export const WithTimestampLabel: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Jane',
      authorLastName: 'Smith',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=5',
    }),
    timestamp: '2024-02-17T10:30:00Z',
    timestampLabel: 'Posted on',
    content: mockContent,
    reactions: mockReactions,
    headerActions: mockHeaderActions,
  },
};

export const WithoutReactions: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Alice',
      authorLastName: 'Johnson',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=10',
    }),
    timestamp: '2024-02-17T10:30:00Z',
    content: mockContent,
    headerActions: mockHeaderActions,
  },
};

export const WithoutActions: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Bob',
      authorLastName: 'Wilson',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=15',
    }),
    timestamp: '2024-02-17T10:30:00Z',
    content: mockContent,
    reactions: mockReactions,
  },
};

export const CustomClassName: Story = {
  args: {
    avatarChildren: createAvatar({
      authorFirstName: 'Emma',
      authorLastName: 'Davis',
      authorPictureUrl: 'https://i.pravatar.cc/300?img=20',
    }),
    timestamp: '2024-02-17T10:30:00Z',
    content: mockContent,
    className: 'bg-secondary p-6 rounded-xl',
  },
};
