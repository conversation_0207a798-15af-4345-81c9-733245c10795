import type { Meta, StoryFn } from '@storybook/nextjs';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { VersionUpdater } from '@kit/ui/version-updater';

// Create a new QueryClient instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
});

const meta: Meta<typeof VersionUpdater> = {
  title: 'Organisms/VersionUpdater',
  component: VersionUpdater,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <Story />
      </QueryClientProvider>
    ),
  ],
};

export default meta;

// Default version updater with standard interval
export const Default: StoryFn<typeof VersionUpdater> = () => <VersionUpdater />;

// Version updater with custom interval
export const CustomInterval: StoryFn<typeof VersionUpdater> = () => (
  <VersionUpdater intervalTimeInSecond={30} />
);

// Version updater with mocked version change
export const VersionChanged: StoryFn<typeof VersionUpdater> = () => {
  // Mock the version endpoint to simulate a version change
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;
    let callCount = 0;

    window.fetch = async (input) => {
      if (input === '/version') {
        callCount++;
        // Return different versions to simulate an update
        return new Response(callCount === 1 ? '1.0.0' : '1.0.1');
      }
      return originalFetch(input);
    };
  }

  return <VersionUpdater intervalTimeInSecond={5} />;
};
