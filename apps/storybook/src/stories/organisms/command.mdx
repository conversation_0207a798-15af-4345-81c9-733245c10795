import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as CommandStories from './command.stories';

<Meta of={CommandStories} />

# Command

A command palette component built on top of [cmdk](https://cmdk.paco.me/). Provides a powerful interface for searching and executing commands with keyboard navigation.

## Features

- Command palette interface
- Search functionality
- Keyboard navigation
- Grouped commands
- Keyboard shortcuts
- Dialog mode
- Custom styling
- Dark theme compatible
- Accessible by default

## Usage

```tsx
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from '@kit/ui/command';

function MyCommandPalette() {
  return (
    <Command>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Suggestions">
          <CommandItem>Calendar</CommandItem>
          <CommandItem>Search Emoji</CommandItem>
          <CommandItem>Calculator</CommandItem>
        </CommandGroup>
      </CommandList>
    </Command>
  );
}
```

## Examples

### Default

Basic command palette with groups and shortcuts.

<Canvas of={CommandStories.Default} />

### With Dialog

Command palette in a dialog that can be triggered by a button.

<Canvas of={CommandStories.WithDialog} />

### With Search

Interactive search functionality with filtered results.

<Canvas of={CommandStories.WithSearch} />

### With Multiple Groups

Command palette with multiple grouped sections.

<Canvas of={CommandStories.WithMultipleGroups} />

## Component API

<Controls />

## Subcomponents

### Command

The root command menu component.

Props:

- `className`: Additional CSS classes
- All HTML div attributes

### CommandDialog

A dialog wrapper for the command menu.

Props:

- `open`: Controls the open state
- `onOpenChange`: Called when open state changes
- All Dialog props

### CommandInput

The search input field.

Props:

- `value`: Controlled input value
- `onValueChange`: Called when value changes
- `placeholder`: Input placeholder text
- All HTML input attributes

### CommandList

Container for command items.

Props:

- `className`: Additional CSS classes
- All HTML div attributes

### CommandEmpty

Shown when no results are found.

Props:

- `className`: Additional CSS classes
- All HTML div attributes

### CommandGroup

Groups related command items.

Props:

- `heading`: Group heading text
- `className`: Additional CSS classes
- All HTML div attributes

### CommandItem

Individual command item.

Props:

- `disabled`: Whether the item is disabled
- `onSelect`: Called when item is selected
- `className`: Additional CSS classes
- All HTML div attributes

### CommandShortcut

Displays keyboard shortcuts.

Props:

- `className`: Additional CSS classes
- All HTML span attributes

### CommandSeparator

Visual separator between groups.

Props:

- `className`: Additional CSS classes
- All HTML div attributes

## Accessibility

The Command component follows WAI-ARIA guidelines:

### Keyboard Navigation

- `↑/↓`: Navigate between items
- `Enter`: Select item
- `Esc`: Close command palette
- `Cmd/Ctrl + K`: Open command palette (when using dialog)
- `Tab/Shift+Tab`: Navigate focusable elements
- `Home/End`: Jump to first/last item

### ARIA Attributes

- Uses `role="combobox"` for input
- Uses `role="listbox"` for list
- Uses `role="option"` for items
- Manages focus appropriately
- Provides proper labeling
- Announces search results

## Guidelines

### Usage Guidelines

1. Command Organization

   - Group related commands
   - Use clear, concise labels
   - Add helpful shortcuts
   - Consider common actions
   - Prioritize important commands

2. Search Experience

   - Implement fuzzy search
   - Show relevant results
   - Handle empty states
   - Provide clear feedback
   - Consider typos

3. Keyboard Interaction
   - Support common shortcuts
   - Enable quick access
   - Handle focus properly
   - Provide visual feedback
   - Consider power users

### Best Practices

1. Implementation

   - Initialize with focus
   - Handle loading states
   - Manage state properly
   - Consider async search
   - Clean up event listeners

2. Content

   - Use descriptive labels
   - Add helpful icons
   - Show keyboard shortcuts
   - Group logically
   - Keep it simple

3. Performance
   - Optimize search
   - Debounce input
   - Virtualize long lists
   - Cache results
   - Monitor render performance
