import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import type { JSONContent } from '@tiptap/react';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { But<PERSON> } from '@kit/ui/button';
import { ForumPostCard } from '@kit/ui/dojo/organisms/forum-post-card';
import type { ForumPost } from '@kit/ui/dojo/organisms/forum-post-card';

const meta = {
  title: 'Organisms/ForumPostCard',
  component: ForumPostCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ForumPostCard>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to create Avatar
const createAvatar = (args: {
  authorFirstName: string;
  authorLastName: string;
  authorPictureUrl?: string;
}) => (
  <div className="flex items-center gap-2">
    <Avatar>
      {args.authorPictureUrl && <AvatarImage src={args.authorPictureUrl} />}
      <AvatarFallback>
        {args.authorFirstName?.charAt(0)}
        {args.authorLastName?.charAt(0)}
      </AvatarFallback>
    </Avatar>
    <span className="font-semibold">
      {args.authorFirstName} {args.authorLastName}
    </span>
  </div>
);

// Updated to match the correct ForumPost interface
const basePostData: Omit<
  ForumPost,
  | 'id'
  | 'title'
  | 'content'
  | 'commentCount'
  | 'isPinned'
  | 'status'
  | 'autoSavedAt'
  | 'lastEditedAt'
> = {
  categoryName: 'General Discussion',
  categoryId: 'cat-1',
  createdByUserId: 'user-123',
  authorFirstName: 'John',
  authorLastName: 'Doe',
  authorPictureUrl: 'https://i.pravatar.cc/300',
  slug: 'my-first-post',
  createdAt: '2024-02-17T10:30:00Z',
  updatedAt: '2024-02-17T10:35:00Z',
};

const mockContent: JSONContent = {
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: 'This is a sample post content.',
        },
      ],
    },
  ],
};

const mockReactionTypes = [
  { value: '👍', icon: '👍' },
  { value: '❤️', icon: '❤️' },
  { value: '🎉', icon: '🎉' },
];

const mockReactions = {
  '👍': { count: 5, user_reacted: true },
  '❤️': { count: 2, user_reacted: false },
};

export const Default: Story = {
  args: {
    post: {
      ...basePostData,
      id: 'post-1',
      title: 'My First Post',
      content: mockContent,
      commentCount: 3,
      isPinned: false,
      status: 'published',
    },
    postDetailsLink: '/posts/post-1',
    reactionCounts: mockReactions,
    reactionTypes: mockReactionTypes,
    avatarChildren: createAvatar(basePostData),
    onReaction: () => console.log('Reaction clicked'),
  },
};

export const Pinned: Story = {
  args: {
    post: {
      ...basePostData,
      id: 'post-2',
      title: 'Important Announcement',
      content: mockContent,
      commentCount: 10,
      isPinned: true,
      status: 'published',
      authorFirstName: 'Admin',
      authorLastName: 'User',
    },
    postDetailsLink: '/posts/post-2',
    reactionCounts: { '📌': { count: 1, user_reacted: false } },
    reactionTypes: mockReactionTypes,
    avatarChildren: createAvatar({
      ...basePostData,
      authorFirstName: 'Admin',
      authorLastName: 'User',
    }),
    onReaction: () => console.log('Reaction clicked'),
  },
};

export const Draft: Story = {
  args: {
    post: {
      ...basePostData,
      id: 'post-3',
      title: 'Draft Post - Not Visible Yet',
      content: mockContent,
      commentCount: 0,
      isPinned: false,
      status: 'draft',
      autoSavedAt: '2024-02-17T11:00:00Z',
      authorFirstName: 'Editor',
      authorLastName: 'Person',
    },
    postDetailsLink: '/posts/post-3',
    reactionCounts: {},
    reactionTypes: mockReactionTypes,
    avatarChildren: createAvatar({
      ...basePostData,
      authorFirstName: 'Editor',
      authorLastName: 'Person',
    }),
    onReaction: () => console.log('Reaction clicked'),
    headerActions: (
      <Button variant="secondary" size="sm">
        Edit
      </Button>
    ),
  },
};

export const WithLongContent: Story = {
  args: {
    post: {
      ...basePostData,
      id: 'post-4',
      title: 'Post with Very Long Content',
      content: {
        /* ... long content JSON ... */ type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              { type: 'text', text: 'This is a long text...'.repeat(20) },
            ],
          },
        ],
      },
      commentCount: 5,
      isPinned: false,
      status: 'published',
    },
    postDetailsLink: '/posts/post-4',
    reactionCounts: mockReactions,
    reactionTypes: mockReactionTypes,
    avatarChildren: createAvatar(basePostData),
    onReaction: () => console.log('Reaction clicked'),
  },
};

export const WithoutReactions: Story = {
  args: {
    post: {
      ...basePostData,
      id: 'post-5',
      title: 'No Reactions Here',
      content: mockContent,
      commentCount: 1,
      isPinned: false,
      status: 'published',
    },
    postDetailsLink: '/posts/post-5',
    reactionCounts: {},
    reactionTypes: mockReactionTypes,
    avatarChildren: createAvatar(basePostData),
    onReaction: () => console.log('Reaction clicked'),
  },
};
