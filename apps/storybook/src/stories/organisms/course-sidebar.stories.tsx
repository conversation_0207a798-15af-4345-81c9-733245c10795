import { useState } from 'react';

import type { <PERSON>a, StoryFn } from '@storybook/nextjs';
import {
  ChevronDown,
  ChevronUp,
  FileText,
  FolderOpen,
  MoreHorizontal,
  Video,
} from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarProvider,
} from '@kit/ui/shadcn-sidebar';

// Define mock data for the stories
const mockChapters = [
  {
    id: '1',
    title: 'Chapter 1: Getting Started',
    icon: '📚',
    sequence_order: 1,
    lessons: [
      {
        id: '1-1',
        title: 'Introduction to the Course',
        icon: '📝',
        status: 'published',
        sequence_order: 1,
        chapter_id: '1',
        content_type: 'video',
      },
      {
        id: '1-2',
        title: 'Setting Up Your Environment',
        icon: '🔧',
        status: 'published',
        sequence_order: 2,
        chapter_id: '1',
        content_type: 'video',
      },
    ],
  },
  {
    id: '2',
    title: 'Chapter 2: Core Concepts',
    icon: '🧠',
    sequence_order: 2,
    lessons: [
      {
        id: '2-1',
        title: 'Understanding the Basics',
        icon: '📊',
        status: 'published',
        sequence_order: 1,
        chapter_id: '2',
        content_type: 'video',
      },
      {
        id: '2-2',
        title: 'Advanced Techniques',
        icon: '⚡',
        status: 'draft',
        sequence_order: 2,
        chapter_id: '2',
        content_type: 'exercise',
      },
    ],
  },
];

const mockStandaloneLessons = [
  {
    id: '3-1',
    title: 'Bonus Lesson: Tips & Tricks',
    icon: '💡',
    status: 'published',
    sequence_order: 1,
    chapter_id: null,
    content_type: 'video',
  },
];

const meta = {
  title: 'Course/Organisms/CourseSidebar',
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="h-[600px] w-full">
        <Story />
      </div>
    ),
  ],
} satisfies Meta;

export default meta;

// Basic Course Sidebar Story
export const Default: StoryFn = () => {
  const [currentLessonId, setCurrentLessonId] = useState('1-1');
  const [expandedChapters, setExpandedChapters] = useState(new Set(['1', '2']));

  const handleLessonClick = (lessonId: string) => {
    setCurrentLessonId(lessonId);
  };

  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) => {
      const next = new Set(prev);
      if (next.has(chapterId)) {
        next.delete(chapterId);
      } else {
        next.add(chapterId);
      }
      return next;
    });
  };

  return (
    <SidebarProvider>
      <div className="flex h-full">
        <div className="bg-sidebar flex h-full flex-col border-r">
          <SidebarHeader className="flex items-center justify-between border-b p-2">
            <div className="flex items-center gap-2">
              <button className="bg-primary text-primary-foreground rounded-full p-1">
                <FolderOpen className="h-4 w-4" />
              </button>
              <button className="bg-muted text-muted-foreground rounded-full p-1">
                <FileText className="h-4 w-4" />
              </button>
            </div>
          </SidebarHeader>
          <SidebarContent className="flex-1 overflow-y-auto p-2">
            {/* Render Chapters */}
            {mockChapters.map((chapter) => (
              <SidebarGroup
                key={chapter.id}
                className="mb-2 space-y-1 rounded-xl border-2 border-gray-200 px-2"
              >
                <div className="group flex items-center justify-between px-2 py-1">
                  <div className="grow">
                    <SidebarGroupLabel
                      onClick={() => toggleChapter(chapter.id)}
                      className="w-full"
                    >
                      <div className="flex w-full items-center">
                        <span className="shrink-0">
                          {expandedChapters.has(chapter.id) ? (
                            <ChevronDown className="mr-2 h-4 w-4" />
                          ) : (
                            <ChevronUp className="mr-2 h-4 w-4" />
                          )}
                        </span>
                        <span className="mr-2 shrink-0">
                          {chapter.icon}
                        </span>
                        <span className="text-foreground mr-2 text-base font-bold">
                          {chapter.title}
                        </span>
                      </div>
                    </SidebarGroupLabel>
                  </div>
                  <div className="shrink-0">
                    <DropdownMenu>
                      <DropdownMenuTrigger className="hover:bg-muted p-1">
                        <MoreHorizontal className="h-5 w-5" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Edit Chapter</DropdownMenuItem>
                        <DropdownMenuItem>Add Lesson</DropdownMenuItem>
                        <DropdownMenuItem>Move Up</DropdownMenuItem>
                        <DropdownMenuItem>Move Down</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          Delete Chapter
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {expandedChapters.has(chapter.id) && (
                  <SidebarGroupContent
                    className="block!"
                    style={{ display: 'block' }}
                  >
                    <SidebarMenu className="pl-8">
                      {chapter.lessons.map((lesson) => (
                        <SidebarMenuItem
                          key={lesson.id}
                          className={`rounded-xl border-2 border-gray-200 p-2 ${
                            currentLessonId === lesson.id
                              ? 'bg-primary/10 text-primary font-medium'
                              : 'hover:bg-primary/5 hover:text-primary'
                          }`}
                        >
                          <div className="flex w-full items-center justify-between">
                            <div
                              className="flex flex-1 cursor-pointer items-center px-2 py-1 text-left text-sm"
                              onClick={() => handleLessonClick(lesson.id)}
                            >
                              <span>{lesson.icon}</span>
                              <span className="flex-1 pl-2">
                                {lesson.title}
                              </span>
                              {lesson.status === 'draft' && (
                                <Badge className="ml-2">Draft</Badge>
                              )}
                              {lesson.content_type === 'video' && (
                                <Video className="ml-2 h-4 w-4 opacity-50" />
                              )}
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger className="mr-1">
                                <MoreHorizontal className="h-5 w-5" />
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>Move Up</DropdownMenuItem>
                                <DropdownMenuItem>Move Down</DropdownMenuItem>
                                <DropdownMenuItem>
                                  Move to Chapter
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-destructive">
                                  Delete Lesson
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                )}
              </SidebarGroup>
            ))}

            {/* Render Standalone Lessons */}
            {mockStandaloneLessons.map((lesson) => (
              <div key={lesson.id} className="list-none">
                <SidebarMenuItem
                  className={`rounded-xl border-2 border-gray-200 p-2 ${
                    currentLessonId === lesson.id
                      ? 'bg-primary/10 text-primary font-medium'
                      : 'hover:bg-primary/5 hover:text-primary'
                  }`}
                >
                  <div className="flex w-full items-center justify-between">
                    <div
                      className="flex flex-1 cursor-pointer items-center px-2 py-1 text-left text-sm"
                      onClick={() => handleLessonClick(lesson.id)}
                    >
                      <span>{lesson.icon}</span>
                      <span className="flex-1 pl-2">{lesson.title}</span>
                      {lesson.status === 'draft' && (
                        <Badge className="ml-2">Draft</Badge>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger className="mr-3">
                        <MoreHorizontal className="h-5 w-5" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Move Up</DropdownMenuItem>
                        <DropdownMenuItem>Move Down</DropdownMenuItem>
                        <DropdownMenuItem>Move to Chapter</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          Delete Lesson
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </SidebarMenuItem>
              </div>
            ))}
          </SidebarContent>
        </div>
        <div className="flex-1 p-4">
          <h1 className="text-2xl font-bold">Course Content</h1>
          <p className="text-muted-foreground mt-2">
            Selected Lesson:{' '}
            {
              [
                ...mockChapters.flatMap((c) => c.lessons),
                ...mockStandaloneLessons,
              ].find((l) => l.id === currentLessonId)?.title
            }
          </p>
        </div>
      </div>
    </SidebarProvider>
  );
};

// Collapsed Sidebar Story
export const Collapsed: StoryFn = () => {
  const [currentLessonId, setCurrentLessonId] = useState('1-1');
  const [expandedChapters, setExpandedChapters] = useState(new Set(['1']));

  const handleLessonClick = (lessonId: string) => {
    setCurrentLessonId(lessonId);
  };

  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) => {
      const next = new Set(prev);
      if (next.has(chapterId)) {
        next.delete(chapterId);
      } else {
        next.add(chapterId);
      }
      return next;
    });
  };

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-full">
        <div className="bg-sidebar flex h-full flex-col border-r">
          <SidebarHeader className="flex items-center justify-between border-b p-2">
            <div className="flex items-center gap-2">
              <button className="bg-primary text-primary-foreground rounded-full p-1">
                <FolderOpen className="h-4 w-4" />
              </button>
              <button className="bg-muted text-muted-foreground rounded-full p-1">
                <FileText className="h-4 w-4" />
              </button>
            </div>
          </SidebarHeader>
          <SidebarContent className="flex-1 overflow-y-auto p-2">
            {/* Render Chapters (same as Default but with tooltips) */}
            {mockChapters.map((chapter) => (
              <SidebarGroup
                key={chapter.id}
                className="mb-2 space-y-1 rounded-xl border-2 border-gray-200 px-2"
              >
                {/* Chapter content with tooltips */}
                <div className="group flex items-center justify-between px-2 py-1">
                  <div className="grow">
                    <SidebarGroupLabel
                      onClick={() => toggleChapter(chapter.id)}
                      className="w-full"
                    >
                      <div className="flex w-full items-center">
                        <span className="shrink-0">
                          {expandedChapters.has(chapter.id) ? (
                            <ChevronDown className="mr-2 h-4 w-4" />
                          ) : (
                            <ChevronUp className="mr-2 h-4 w-4" />
                          )}
                        </span>
                        <span className="mr-2 shrink-0">
                          {chapter.icon}
                        </span>
                        <span className="text-foreground sr-only-if-collapsed mr-2 text-base font-bold">
                          {chapter.title}
                        </span>
                      </div>
                    </SidebarGroupLabel>
                  </div>
                </div>

                {expandedChapters.has(chapter.id) && (
                  <SidebarGroupContent
                    className="block!"
                    style={{ display: 'block' }}
                  >
                    <SidebarMenu className="pl-8">
                      {chapter.lessons.map((lesson) => (
                        <SidebarMenuItem
                          key={lesson.id}
                          className={`rounded-xl border-2 border-gray-200 p-2 ${
                            currentLessonId === lesson.id
                              ? 'bg-primary/10 text-primary font-medium'
                              : 'hover:bg-primary/5 hover:text-primary'
                          }`}
                        >
                          <div className="flex w-full items-center justify-between">
                            <div
                              className="flex flex-1 cursor-pointer items-center px-2 py-1 text-left text-sm"
                              onClick={() => handleLessonClick(lesson.id)}
                            >
                              <span>{lesson.icon}</span>
                              <span className="sr-only-if-collapsed flex-1 pl-2">
                                {lesson.title}
                              </span>
                              {lesson.status === 'draft' && (
                                <Badge className="sr-only-if-collapsed ml-2">
                                  Draft
                                </Badge>
                              )}
                            </div>
                          </div>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                )}
              </SidebarGroup>
            ))}
          </SidebarContent>
        </div>
        <div className="flex-1 p-4">
          <h1 className="text-2xl font-bold">Course Content</h1>
          <p className="text-muted-foreground mt-2">
            The sidebar is collapsed to show only icons. Click on them to
            navigate.
          </p>
        </div>
      </div>
    </SidebarProvider>
  );
};

// Without Permissions Story
export const WithoutPermissions: StoryFn = () => {
  const [currentLessonId, setCurrentLessonId] = useState('1-1');
  const [expandedChapters, setExpandedChapters] = useState(new Set(['1']));

  const handleLessonClick = (lessonId: string) => {
    setCurrentLessonId(lessonId);
  };

  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) => {
      const next = new Set(prev);
      if (next.has(chapterId)) {
        next.delete(chapterId);
      } else {
        next.add(chapterId);
      }
      return next;
    });
  };

  return (
    <SidebarProvider>
      <div className="flex h-full">
        <div className="bg-sidebar flex h-full flex-col border-r">
          <SidebarHeader className="flex items-center justify-between border-b p-2">
            <div className="flex items-center gap-2">
              {/* No create buttons since there's no permission */}
            </div>
          </SidebarHeader>
          <SidebarContent className="flex-1 overflow-y-auto p-2">
            {/* Render Chapters without action menus */}
            {mockChapters.map((chapter) => (
              <SidebarGroup
                key={chapter.id}
                className="mb-2 space-y-1 rounded-xl border-2 border-gray-200 px-2"
              >
                <div className="group flex items-center px-2 py-1">
                  <div className="grow">
                    <SidebarGroupLabel
                      onClick={() => toggleChapter(chapter.id)}
                      className="w-full"
                    >
                      <div className="flex w-full items-center">
                        <span className="shrink-0">
                          {expandedChapters.has(chapter.id) ? (
                            <ChevronDown className="mr-2 h-4 w-4" />
                          ) : (
                            <ChevronUp className="mr-2 h-4 w-4" />
                          )}
                        </span>
                        <span className="mr-2 shrink-0">
                          {chapter.icon}
                        </span>
                        <span className="text-foreground mr-2 text-base font-bold">
                          {chapter.title}
                        </span>
                      </div>
                    </SidebarGroupLabel>
                  </div>
                  {/* No dropdown menu since there's no permission */}
                </div>

                {expandedChapters.has(chapter.id) && (
                  <SidebarGroupContent
                    className="block!"
                    style={{ display: 'block' }}
                  >
                    <SidebarMenu className="pl-8">
                      {chapter.lessons.map((lesson) => (
                        <SidebarMenuItem
                          key={lesson.id}
                          className={`rounded-xl border-2 border-gray-200 p-2 ${
                            currentLessonId === lesson.id
                              ? 'bg-primary/10 text-primary font-medium'
                              : 'hover:bg-primary/5 hover:text-primary'
                          }`}
                        >
                          <div className="flex w-full items-center justify-between">
                            <div
                              className="flex flex-1 cursor-pointer items-center px-2 py-1 text-left text-sm"
                              onClick={() => handleLessonClick(lesson.id)}
                            >
                              <span>{lesson.icon}</span>
                              <span className="flex-1 pl-2">
                                {lesson.title}
                              </span>
                              {lesson.status === 'draft' && (
                                <Badge className="ml-2">Draft</Badge>
                              )}
                            </div>
                            {/* No dropdown menu since there's no permission */}
                          </div>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                )}
              </SidebarGroup>
            ))}
          </SidebarContent>
        </div>
        <div className="flex-1 p-4">
          <h1 className="text-2xl font-bold">Course Content</h1>
          <p className="text-muted-foreground mt-2">
            View-only mode: No edit, create, or delete actions are available.
          </p>
        </div>
      </div>
    </SidebarProvider>
  );
};
