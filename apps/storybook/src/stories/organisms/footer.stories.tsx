import Image from 'next/image';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Footer } from '@kit/ui/dojo/organisms/footer';

const meta = {
  title: 'Organisms/Footer',
  component: Footer,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Footer>;

export default meta;
type Story = StoryObj<typeof Footer>;

const Logo = () => (
  <div className="relative h-8 w-8">
    <Image
      src="https://i.pravatar.cc/200"
      alt="Logo"
      fill
      className="rounded-lg object-cover"
    />
  </div>
);

const defaultSections = [
  {
    heading: 'Product',
    links: [
      { href: '#', label: 'Features' },
      { href: '#', label: 'Pricing' },
      { href: '#', label: 'Documentation' },
      { href: '#', label: 'Changelog' },
    ],
  },
  {
    heading: 'Company',
    links: [
      { href: '#', label: 'About' },
      { href: '#', label: 'Blog' },
      { href: '#', label: 'Careers' },
      { href: '#', label: 'Contact' },
    ],
  },
  {
    heading: 'Resources',
    links: [
      { href: '#', label: 'Community' },
      { href: '#', label: 'Help Center' },
      { href: '#', label: 'Partners' },
      { href: '#', label: 'Status' },
    ],
  },
  {
    heading: 'Legal',
    links: [
      { href: '#', label: 'Privacy' },
      { href: '#', label: 'Terms' },
      { href: '#', label: 'License' },
      { href: '#', label: 'Cookie Policy' },
    ],
  },
];

export const Default: Story = {
  args: {
    logo: <Logo />,
    description:
      'Build better products faster with our comprehensive development platform.',
    copyright: '© 2024 Your Company, Inc. All rights reserved.',
    sections: defaultSections,
  },
};

export const WithFewerSections: Story = {
  args: {
    ...Default.args,
    sections: defaultSections.slice(0, 2),
  },
};

export const WithCustomDescription: Story = {
  args: {
    ...Default.args,
    description: (
      <div className="flex flex-col space-y-4">
        <p>Your trusted development partner.</p>
        <div className="flex space-x-4">
          <a href="#" className="text-muted-foreground hover:text-primary">
            Twitter
          </a>
          <a href="#" className="text-muted-foreground hover:text-primary">
            GitHub
          </a>
          <a href="#" className="text-muted-foreground hover:text-primary">
            Discord
          </a>
        </div>
      </div>
    ),
  },
};
