'use client';

import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';

import { Button } from '@kit/ui/button';
import type { Column, ColumnDef, Row } from '@kit/ui/data-table';
import { DataTable } from '@kit/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

// Example data type
interface Payment {
  id: string;
  amount: number;
  status: 'pending' | 'processing' | 'success' | 'failed';
  email: string;
}

// Example data
const data: Payment[] = [
  {
    id: '1',
    amount: 100,
    status: 'pending',
    email: '<EMAIL>',
  },
  {
    id: '2',
    amount: 200,
    status: 'success',
    email: '<EMAIL>',
  },
  {
    id: '3',
    amount: 300,
    status: 'failed',
    email: '<EMAIL>',
  },
  {
    id: '4',
    amount: 400,
    status: 'processing',
    email: '<EMAIL>',
  },
];

// Example columns
const columns: ColumnDef<Payment>[] = [
  {
    id: 'status',
    accessorFn: (row: Payment) => row.status,
    header: 'Status',
  },
  {
    id: 'email',
    accessorFn: (row: Payment) => row.email,
    header: ({ column }: { column: Column<Payment> }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    id: 'amount',
    accessorFn: (row: Payment) => row.amount,
    header: () => <div className="text-right">Amount</div>,
    cell: ({ row }: { row: Row<Payment> }) => {
      const amount = parseFloat(row.getValue('amount'));
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
      return <div className="text-right font-medium">{formatted}</div>;
    },
  },
  {
    id: 'actions',
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(payment.id)}
            >
              Copy payment ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View customer</DropdownMenuItem>
            <DropdownMenuItem>View payment details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

const meta = {
  title: 'Organisms/DataTable',
  component: DataTable,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof DataTable>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic DataTable
export const Basic: Story = {
  args: {
    columns: columns as ColumnDef<unknown>[],
    data,
  },
};
