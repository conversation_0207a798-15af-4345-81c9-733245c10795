import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as SonnerStories from './sonner.stories';

<Meta of={SonnerStories} />

# Sonner Toast

The Sonner Toast component is a customized toast notification system built on top of the Sonner library. It provides a beautiful, accessible way to show toast notifications with support for different types, positions, and themes.

## Features

- Multiple toast types (default, success, error)
- Rich colors support
- Customizable positions
- Dark mode compatible
- Accessible notifications
- Action buttons support
- Responsive design
- Theme-aware styling

## Usage

```tsx
import { toast } from 'sonner';

import { Toaster } from '@kit/ui/sonner';

export default function Layout() {
  return (
    <>
      <Toaster richColors position="top-center" />
      {/* Your app content */}
    </>
  );
}

// Showing a toast
function ShowToast() {
  return (
    <button
      onClick={() =>
        toast('Event created', {
          description: 'Your event has been scheduled',
        })
      }
    >
      Create Event
    </button>
  );
}
```

## Examples

### Default Toast

Basic toast notification with a title and description.

<Canvas of={SonnerStories.Default} />

### Success Toast

Toast notification for successful operations.

<Canvas of={SonnerStories.WithSuccess} />

### Error Toast

Toast notification for error states.

<Canvas of={SonnerStories.WithError} />

### Custom Position

Toast with custom position (bottom-right).

<Canvas of={SonnerStories.WithCustomPosition} />

### With Action

Toast with an action button.

<Canvas of={SonnerStories.WithAction} />

## Component API

<Controls />

## Toast Types

```tsx
// Default toast
toast('Message');

// Success toast
toast.success('Success message');

// Error toast
toast.error('Error message');

// With description
toast('Message', {
  description: 'Additional details here',
});

// With action
toast('Message', {
  action: {
    label: 'Undo',
    onClick: () => console.log('Undo clicked'),
  },
});
```

## Accessibility

The Sonner Toast component follows accessibility best practices:

- ARIA live regions for screen readers
- Keyboard navigation support
- High contrast colors
- Proper focus management
- Role and aria-label attributes
- Automatic dismissal with appropriate timing

## Best Practices

1. Keep toast messages concise and clear
2. Use appropriate toast types for different scenarios
3. Include helpful descriptions when needed
4. Position toasts consistently throughout your app
5. Provide action buttons for reversible operations
6. Consider mobile viewports when choosing positions
7. Use appropriate timing for different message types

## Customization

The component can be customized using props and className:

```tsx
<Toaster
  position="bottom-left"
  richColors
  theme="dark"
  className="custom-toaster"
  toastOptions={{
    classNames: {
      toast: 'custom-toast-class',
      description: 'custom-description-class',
      actionButton: 'custom-action-button-class',
      cancelButton: 'custom-cancel-button-class',
    },
  }}
/>
```

## Integration

The Toaster component should be placed in your root layout:

```tsx
// app/layout.tsx
import { Toaster } from '@kit/ui/sonner';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html>
      <body>
        {children}
        <Toaster richColors theme="system" />
      </body>
    </html>
  );
}
```
