import type { Meta, StoryObj } from '@storybook/nextjs';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Toaster } from '@kit/ui/sonner';

const meta = {
  title: 'Organisms/Sonner',
  component: Toaster,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div>
        <Story />
        <div className="flex flex-col gap-4">
          <Button
            onClick={() =>
              toast('Event has been created', {
                description: 'Sunday, December 03, 2023 at 9:00 AM',
              })
            }
          >
            Show Toast
          </Button>
        </div>
      </div>
    ),
  ],
} satisfies Meta<typeof Toaster>;

export default meta;
type Story = StoryObj<typeof Toaster>;

export const Default: Story = {
  args: {
    richColors: true,
    position: 'top-center',
  },
};

export const WithSuccess: Story = {
  args: {
    richColors: true,
    position: 'top-center',
  },
  decorators: [
    (Story) => (
      <div>
        <Story />
        <div className="flex flex-col gap-4">
          <Button
            onClick={() =>
              toast.success('Profile updated', {
                description: 'Your changes have been saved successfully',
              })
            }
          >
            Show Success Toast
          </Button>
        </div>
      </div>
    ),
  ],
};

export const WithError: Story = {
  args: {
    richColors: true,
    position: 'top-center',
  },
  decorators: [
    (Story) => (
      <div>
        <Story />
        <div className="flex flex-col gap-4">
          <Button
            onClick={() =>
              toast.error('Error occurred', {
                description: 'Failed to save changes. Please try again.',
              })
            }
          >
            Show Error Toast
          </Button>
        </div>
      </div>
    ),
  ],
};

export const WithCustomPosition: Story = {
  args: {
    richColors: true,
    position: 'bottom-right',
  },
  decorators: [
    (Story) => (
      <div>
        <Story />
        <div className="flex flex-col gap-4">
          <Button
            onClick={() =>
              toast('Notification', {
                description: 'This toast appears in the bottom right',
              })
            }
          >
            Show Bottom Right Toast
          </Button>
        </div>
      </div>
    ),
  ],
};

export const WithAction: Story = {
  args: {
    richColors: true,
    position: 'top-center',
  },
  decorators: [
    (Story) => (
      <div>
        <Story />
        <div className="flex flex-col gap-4">
          <Button
            onClick={() =>
              toast('File deleted', {
                description: 'The file has been moved to trash',
                action: {
                  label: 'Undo',
                  onClick: () => console.log('Undo clicked'),
                },
              })
            }
          >
            Show Toast with Action
          </Button>
        </div>
      </div>
    ),
  ],
};
