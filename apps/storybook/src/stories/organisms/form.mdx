import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as FormStories from './form.stories';

<Meta of={FormStories} />

# Form

The Form component provides a powerful way to build forms with validation, error handling, and type safety. Built on top of `react-hook-form` and `zod`, it offers a robust foundation for creating accessible and type-safe forms.

## Features

- Built with React Hook Form
- Zod schema validation
- Type-safe form fields
- Accessible by default
- Error handling and messages
- Field descriptions
- Various input types support
- Dark theme compatible

## Usage

```tsx
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';

// Define your form schema
const formSchema = z.object({
  username: z.string().min(2, 'Username must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
});

function MyForm() {
  // Initialize form with schema
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => console.log(data))}>
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Add more fields */}
      </form>
    </Form>
  );
}
```

## Examples

### Basic Form

A basic form with validation and error handling.

<Canvas of={FormStories.Default} />

### With Prefilled Data

Form initialized with default values.

<Canvas of={FormStories.WithPrefilledData} />

## Component API

### Form

The root form component that provides form context.

Props:

- All props from `react-hook-form`'s `FormProvider`

### FormField

A field wrapper that handles form control state.

Props:

- `control`: Form control from `useForm`
- `name`: Field name
- `render`: Render function for the field

### FormItem

Container for form field elements.

Props:

- `className`: Additional CSS classes

### FormLabel

Label for form fields.

Props:

- All `Label` component props

### FormControl

Wrapper for form inputs.

Props:

- All `Slot` component props

### FormDescription

Helper text for form fields.

Props:

- `className`: Additional CSS classes

### FormMessage

Error message display for form fields.

Props:

- `className`: Additional CSS classes

## Accessibility

The Form component follows WAI-ARIA guidelines:

- Proper labeling with `htmlFor`
- Error message association
- Focus management
- Screen reader announcements
- Keyboard navigation

## Guidelines

### Form Structure

1. Field Organization

   - Group related fields
   - Use clear labels
   - Add helpful descriptions
   - Show validation rules
   - Handle errors gracefully

2. Validation

   - Define clear rules
   - Show immediate feedback
   - Provide helpful messages
   - Handle edge cases
   - Support async validation

3. Error Handling
   - Show clear messages
   - Indicate field status
   - Maintain context
   - Guide user to fix
   - Support recovery

### Best Practices

1. Schema Definition

   - Use clear types
   - Define strict rules
   - Add helpful messages
   - Consider edge cases
   - Handle transformations

2. Form State

   - Track dirty state
   - Handle submissions
   - Manage validation
   - Control focus
   - Handle resets

3. Performance
   - Optimize validation
   - Manage re-renders
   - Handle large forms
   - Cache results
   - Clean up resources

<Controls />{' '}
