import Image from 'next/image';

import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Zap } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Hero } from '@kit/ui/dojo/organisms/hero';

const meta = {
  title: 'Organisms/Hero',
  component: Hero,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Hero>;

export default meta;
type Story = StoryObj<typeof Hero>;

const Pill = () => (
  <div className="bg-muted inline-flex items-center rounded-full border px-3 py-1 text-sm">
    <Zap className="mr-2 h-3 w-3" />
    New Features
  </div>
);

export const Default: Story = {
  args: {
    pill: <Pill />,
    title: 'Build Better Products Faster',
    subtitle: 'The modern platform for rapid development and innovation.',
    cta: (
      <div className="flex gap-4">
        <Button size="lg">Get Started</Button>
        <Button size="lg" variant="outline">
          Learn More
        </Button>
      </div>
    ),
  },
};

export const WithImage: Story = {
  args: {
    ...Default.args,
    image: (
      <div className="relative h-[400px] w-full max-w-3xl overflow-hidden rounded-lg shadow-2xl">
        <Image
          src="https://images.unsplash.com/photo-1498050108023-c5249f4df085"
          alt="Hero"
          fill
          className="object-cover"
        />
      </div>
    ),
  },
};

export const WithoutAnimation: Story = {
  args: {
    ...Default.args,
    animate: false,
  },
};

export const Minimal: Story = {
  args: {
    title: 'Simple and Powerful',
    subtitle: 'Everything you need to build amazing products.',
  },
};
