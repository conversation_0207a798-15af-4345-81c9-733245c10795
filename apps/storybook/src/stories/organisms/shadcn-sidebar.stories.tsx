import type { Meta, StoryFn } from '@storybook/nextjs';
import { Home, Settings, Users } from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@kit/ui/shadcn-sidebar';

const meta = {
  title: 'Organisms/ShadcnSidebar',
  component: Sidebar,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="h-[600px] w-full">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Sidebar>;

export default meta;

// Default sidebar with basic navigation
export const Default: StoryFn<typeof Sidebar> = () => {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <Sidebar>
          <SidebarHeader>
            <div className="px-2 py-4">
              <h2 className="text-lg font-semibold">My App</h2>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Main Navigation</SidebarGroupLabel>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Home />
                    <span>Home</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Users />
                    <span>Users</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Settings />
                    <span>Settings</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroup>
          </SidebarContent>
          <SidebarFooter>
            <div className="p-2">
              <p className="text-muted-foreground text-xs">Version 1.0.0</p>
            </div>
          </SidebarFooter>
        </Sidebar>
        <SidebarInset>
          <div className="p-6">
            <h1 className="text-2xl font-bold">Main Content</h1>
            <p className="text-muted-foreground mt-2">
              This is the main content area that adapts to the sidebar state.
            </p>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

// Collapsible sidebar that can be toggled
export const Collapsible: StoryFn<typeof Sidebar> = () => {
  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-full">
        <Sidebar collapsible="icon">
          <SidebarHeader>
            <div className="flex items-center justify-between px-2 py-4">
              <h2 className="text-lg font-semibold">My App</h2>
              <SidebarTrigger />
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Main Navigation</SidebarGroupLabel>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Home />
                    <span>Home</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Users />
                    <span>Users</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Settings />
                    <span>Settings</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>
        <SidebarInset>
          <div className="p-6">
            <h1 className="text-2xl font-bold">Main Content</h1>
            <p className="text-muted-foreground mt-2">
              Click the trigger button to toggle the sidebar.
            </p>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

// Floating variant of the sidebar
export const Floating: StoryFn<typeof Sidebar> = () => {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <Sidebar variant="floating">
          <SidebarHeader>
            <div className="px-2 py-4">
              <h2 className="text-lg font-semibold">My App</h2>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Main Navigation</SidebarGroupLabel>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Home />
                    <span>Home</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Users />
                    <span>Users</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Settings />
                    <span>Settings</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>
        <SidebarInset>
          <div className="p-6">
            <h1 className="text-2xl font-bold">Main Content</h1>
            <p className="text-muted-foreground mt-2">
              The sidebar has a floating appearance with shadow and border.
            </p>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

// Minimized sidebar that expands on hover
export const MinimizedExpandOnHover: StoryFn<typeof Sidebar> = () => {
  return (
    <SidebarProvider minimized expandOnHover>
      <div className="flex h-full">
        <Sidebar>
          <SidebarHeader>
            <div className="px-2 py-4">
              <h2 className="text-lg font-semibold transition-opacity group-data-[minimized=true]:opacity-0">
                My App
              </h2>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel className="transition-opacity group-data-[minimized=true]:opacity-0">
                Main Navigation
              </SidebarGroupLabel>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Home className="shrink-0" />
                    <span className="transition-opacity group-data-[minimized=true]:opacity-0">
                      Home
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Users className="shrink-0" />
                    <span className="transition-opacity group-data-[minimized=true]:opacity-0">
                      Users
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <Settings className="shrink-0" />
                    <span className="transition-opacity group-data-[minimized=true]:opacity-0">
                      Settings
                    </span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>
        <SidebarInset>
          <div className="p-6">
            <h1 className="text-2xl font-bold">Main Content</h1>
            <p className="text-muted-foreground mt-2">
              The sidebar is minimized to show only icons. Hover over it to
              expand and show labels.
            </p>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};
