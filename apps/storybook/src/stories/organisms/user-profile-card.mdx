import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>a, Story } from '@storybook/addon-docs/blocks';

import { UserProfileCard } from '@kit/ui/dojo/organisms/user-profile-card';

import * as Stories from './user-profile-card.stories';

<Meta of={Stories} />

# User Profile Card

The User Profile Card component displays comprehensive information about a user's profile, including their personal details, biography, social media links, and membership information.

## Usage

```tsx
import { UserProfileCard } from '@kit/ui/dojo/organisms/user-profile-card';

export function UserProfile() {
  // Example publicProfile data from API
  const publicProfile = {
    id: '123',
    name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    slug: 'johndoe', // Used for username display
    picture_url: 'https://example.com/avatar.jpg',
    created_at: '2022-01-15T12:00:00Z',
    last_sign_in_at: '2023-11-05T14:30:00Z',
    updated_at: '2023-10-20T09:15:00Z',
    country: 'United States',
    country_icon: '🇺🇸',
    user_details: {
      bio: 'Full-stack developer with a passion for creating amazing web experiences.',
      socials: [
        { name: 'github', url: 'https://github.com/johndoe' },
        { name: 'twitter', url: 'https://twitter.com/johndoe' },
        { name: 'linkedin', url: 'https://linkedin.com/in/johndoe' },
      ],
    },
  };

  return <UserProfileCard publicProfile={publicProfile} />;
}
```

## Examples

### Default

Basic display with minimal information:

<Canvas>
  <Story of={Stories.Default} />
</Canvas>

### Complete Profile

A user profile card with all fields filled out, including picture, bio, location, and social links.

<Canvas>
  <Story of={Stories.CompleteProfile} />
</Canvas>

### With Bio Only

A simple profile card showing only the user's biography.

<Canvas>
  <Story of={Stories.WithPictureAndBio} />
</Canvas>

### With Social Links

A profile card including links to the user's social media profiles.

<Canvas>
  <Story of={Stories.WithSocialLinks} />
</Canvas>

### Custom Styling

An example demonstrating how to apply custom CSS classes to the card.

<Canvas>
  <Story of={Stories.Default} />
</Canvas>

### No Profile Picture

A profile card without a user picture, showing the default avatar or initials.

<Canvas>
  <Story of={Stories.Default} />
</Canvas>

### No Slug (Username)

An edge case showing the card's appearance when the user's slug (username) is missing.

<Canvas>
  <Story of={Stories.MissingSlug} />
</Canvas>

## Component Architecture

This component is designed as an organism in the Atomic Design methodology, comprising multiple smaller components:

- Avatar (from @kit/ui/avatar)
- Badge (from @kit/ui/badge)
- Card components (from @kit/ui/card)
- SocialLinks molecule (from @kit/ui/dojo/molecules)
- Separator (from @kit/ui/separator)

## API Reference

<Controls />

## Data Structure

The component accepts a `publicProfile` object that contains user profile data. The component handles extracting and formatting the following data points:

- `slug` (primary field for username display)
- Name and last name (for displaying full name)
- Profile picture URL
- Bio (from user_details.bio)
- Social links (from user_details.socials)
- Dates (created_at, last_sign_in_at, updated_at)
- Country and country icon

The component will use the `slug` field from the profile for displaying the username. If no slug is available, it will display "unknown" as the username.

## Accessibility

The User Profile Card component includes appropriate data-test attributes for testing and maintains a semantic structure for screen readers.
