import type { Meta, StoryObj } from '@storybook/nextjs';

import { VideoPlayer } from '@kit/ui/dojo/organisms/video-player';

const meta: Meta<typeof VideoPlayer> = {
  title: 'Organisms/VideoPlayer',
  component: VideoPlayer,
  tags: ['autodocs'],
  args: {
    videoUrl: 'https://www.youtube.com/watch?v=ysz5S6PUM-U', // Example YouTube video
    shortCutKeysEnabled: true,
    additionalControls: <button>Test Button</button>, // Example additional control
  },
};

export default meta;
type Story = StoryObj<typeof VideoPlayer>;

export const Default: Story = {
  args: {},
};

export const WithShortcutsDisabled: Story = {
  args: {
    shortCutKeysEnabled: false,
  },
};

export const WithoutAdditionalControls: Story = {
  args: {
    additionalControls: undefined,
  },
};
