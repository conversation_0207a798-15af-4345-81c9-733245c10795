import { Can<PERSON>, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as HeroTitleStories from './hero-title.stories';

<Meta of={HeroTitleStories} />

# Hero Title

The HeroTitle component is a specialized heading component designed for use in hero sections, featuring responsive typography and support for custom styling.

## Features

- Responsive font sizing
- Support for custom styling
- Dark mode compatible
- Support for rich text content
- Radix Slot integration for composition

## Usage

```tsx
import { HeroTitle } from '@kit/ui/hero-title';

export default function Page() {
  return <HeroTitle>Your Headline Text</HeroTitle>;
}
```

## Examples

### Default Title

A basic hero title with default styling.

<Canvas of={HeroTitleStories.Default} />

### With Gradient Highlight

A hero title with a gradient-highlighted word.

<Canvas of={HeroTitleStories.WithHighlight} />

### Multiline Title

A hero title split across multiple lines.

<Canvas of={HeroTitleStories.Multiline} />

### Custom Styled Title

A hero title with custom styling applied.

<Canvas of={HeroTitleStories.WithCustomClassName} />

## Component API

<Controls />

## Accessibility

The HeroTitle component follows accessibility best practices:

- Uses semantic h1 tag by default
- Maintains proper heading hierarchy
- Supports ARIA attributes
- Ensures sufficient color contrast
- Responsive text sizing for readability

## Best Practices

1. Keep titles concise and impactful
2. Use appropriate heading hierarchy
3. Ensure text remains readable at all screen sizes
4. Test color contrast with custom styles
5. Consider line length for multiline titles

## Composition

The HeroTitle component can be composed with other components using the `asChild` prop:

```tsx
import { HeroTitle } from '@kit/ui/hero-title';
import { Link } from '@kit/ui/link';

<HeroTitle asChild>
  <Link href="/about">About Us</Link>
</HeroTitle>;
```
