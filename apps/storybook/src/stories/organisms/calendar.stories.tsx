'use client';

import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { addDays } from 'date-fns';

import type { DateRange } from '@kit/ui/calendar';
import { Calendar } from '@kit/ui/calendar';

const meta = {
  title: 'Organisms/Calendar',
  component: Calendar,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Calendar>;

export default meta;
type Story = StoryObj<typeof meta>;

type CalendarDemoProps = {
  mode?: 'single' | 'range' | 'multiple';
  className?: string;
  disabled?: { from: Date; to: Date }[];
  footer?: React.ReactNode;
};

// Interactive wrapper component for the stories
const CalendarDemo = ({
  mode = 'single',
  className,
  disabled,
  footer,
}: CalendarDemoProps) => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(),
    to: undefined,
  });
  const [dates, setDates] = useState<Date[]>([new Date()]);

  if (mode === 'range') {
    return (
      <Calendar
        mode="range"
        selected={dateRange}
        onSelect={setDateRange}
        className={className}
        disabled={disabled}
        footer={footer}
      />
    );
  }

  if (mode === 'multiple') {
    return (
      <Calendar
        mode="multiple"
        selected={dates}
        onSelect={(dates) => dates && setDates(dates)}
        className={className}
        disabled={disabled}
        footer={footer}
      />
    );
  }

  return (
    <Calendar
      mode="single"
      selected={date}
      onSelect={setDate}
      className={className}
      disabled={disabled}
      footer={footer}
    />
  );
};

export const Default: Story = {
  render: () => <CalendarDemo className="rounded-md border" />,
};

export const Range: Story = {
  render: () => <CalendarDemo mode="range" className="rounded-md border" />,
};

export const Multiple: Story = {
  render: () => <CalendarDemo mode="multiple" className="rounded-md border" />,
};

export const WithDisabledDates: Story = {
  render: () => (
    <CalendarDemo
      className="rounded-md border"
      disabled={[{ from: addDays(new Date(), 5), to: addDays(new Date(), 10) }]}
    />
  ),
};

export const WithFooter: Story = {
  render: () => (
    <CalendarDemo
      className="rounded-md border"
      footer={<p className="text-center text-sm">Click to select a date</p>}
    />
  ),
};

export const WithCustomStyles: Story = {
  render: () => (
    <CalendarDemo className="bg-card text-card-foreground rounded-md border shadow-xs" />
  ),
};
