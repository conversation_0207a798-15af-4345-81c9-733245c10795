import Image from 'next/image';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Button } from '@kit/ui/button';
import { Header } from '@kit/ui/dojo/organisms/header';

const meta = {
  title: 'Organisms/Header',
  component: Header,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Header>;

export default meta;
type Story = StoryObj<typeof Header>;

const Logo = () => (
  <div className="relative h-8 w-8">
    <Image
      src="https://i.pravatar.cc/200"
      alt="Logo"
      fill
      className="rounded-lg object-cover"
    />
  </div>
);

const Navigation = () => (
  <nav className="hidden items-center justify-center space-x-8 text-sm md:flex">
    <a href="#" className="text-foreground hover:text-primary">
      Features
    </a>
    <a href="#" className="text-foreground hover:text-primary">
      Pricing
    </a>
    <a href="#" className="text-foreground hover:text-primary">
      About
    </a>
    <a href="#" className="text-foreground hover:text-primary">
      Contact
    </a>
  </nav>
);

const Actions = () => (
  <div className="flex items-center space-x-4">
    <Button variant="ghost" size="sm">
      Sign In
    </Button>
    <Button size="sm">Get Started</Button>
  </div>
);

export const Default: Story = {
  args: {
    logo: <Logo />,
    navigation: <Navigation />,
    actions: <Actions />,
  },
};

export const WithoutNavigation: Story = {
  args: {
    logo: <Logo />,
    actions: <Actions />,
  },
};

export const WithoutActions: Story = {
  args: {
    logo: <Logo />,
    navigation: <Navigation />,
  },
};

export const LogoOnly: Story = {
  args: {
    logo: <Logo />,
  },
};
