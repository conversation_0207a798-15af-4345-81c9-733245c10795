import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs';
import { <PERSON>pt<PERSON>, Shield, Zap } from 'lucide-react';

import { FeatureGrid } from '@kit/ui/dojo/organisms/feature-grid';

const meta = {
  title: 'Organisms/Feature Grid',
  component: FeatureGrid,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof FeatureGrid>;

export default meta;
type Story = StoryObj<typeof FeatureGrid>;

const FeatureCard = ({
  icon,
  title,
  description,
}: {
  icon?: React.ReactNode;
  title: string;
  description: string;
}) => (
  <div className="bg-card relative flex flex-col gap-4 rounded-2xl border p-6 shadow-xs">
    {icon && (
      <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
        {icon}
      </div>
    )}
    <h3 className="text-card-foreground text-lg font-semibold">{title}</h3>
    <p className="text-muted-foreground text-sm">{description}</p>
  </div>
);

const defaultFeatures = [
  {
    title: 'Lightning Fast',
    description: 'Built for speed and performance, delivering instant results.',
    icon: <Zap className="text-primary h-5 w-5" />,
  },
  {
    title: 'Secure by Design',
    description: 'Enterprise-grade security built into every feature.',
    icon: <Shield className="text-primary h-5 w-5" />,
  },
  {
    title: 'Modern Interface',
    description: 'Clean, intuitive design that works across all devices.',
    icon: <Laptop className="text-primary h-5 w-5" />,
  },
];

export const Default: Story = {
  args: {
    children: defaultFeatures.map((feature, index) => (
      <FeatureCard key={index} {...feature} />
    )),
  },
};

export const TwoColumns: Story = {
  args: {
    className: 'grid-cols-1 md:grid-cols-2',
    children: defaultFeatures.map((feature, index) => (
      <FeatureCard key={index} {...feature} />
    )),
  },
};

export const FourColumns: Story = {
  args: {
    className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    children: [
      ...defaultFeatures,
      {
        title: 'Customizable',
        description: 'Adapt the interface to match your brand and needs.',
        icon: <Zap className="text-primary h-5 w-5" />,
      },
    ].map((feature, index) => <FeatureCard key={index} {...feature} />),
  },
};

export const WithoutIcons: Story = {
  args: {
    children: defaultFeatures.map(({ title, description }, index) => (
      <FeatureCard key={index} title={title} description={description} />
    )),
  },
};
