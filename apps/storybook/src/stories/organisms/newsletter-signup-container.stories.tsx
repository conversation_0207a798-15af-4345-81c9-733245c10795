import type { Meta, StoryObj } from '@storybook/nextjs';

import { NewsletterSignupContainer } from '@kit/ui/dojo/organisms/newsletter-signup-container';

const meta = {
  title: 'Organisms/Newsletter Signup Container',
  component: NewsletterSignupContainer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof NewsletterSignupContainer>;

export default meta;
type Story = StoryObj<typeof NewsletterSignupContainer>;

const mockSignup = async (email: string) => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 1000));
  if (email.includes('error')) {
    throw new Error('Signup failed');
  }
};

export const Default: Story = {
  args: {
    onSignup: mockSignup,
  },
};

export const WithCustomMessages: Story = {
  args: {
    onSignup: mockSignup,
    heading: 'Join Our Community',
    description: 'Stay up to date with our latest features and announcements.',
    successMessage:
      'Welcome to our community! Check your inbox for confirmation.',
    errorMessage: 'Oops! Something went wrong. Please try again later.',
  },
};

export const WithCustomStyling: Story = {
  args: {
    onSignup: mockSignup,
    className: 'max-w-lg bg-muted p-8 rounded-xl',
    heading: 'Stay in the Loop',
    description:
      'Get exclusive access to beta features and early announcements.',
  },
};
