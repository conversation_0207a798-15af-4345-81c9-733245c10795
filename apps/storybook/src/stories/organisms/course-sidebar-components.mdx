import { <PERSON><PERSON>, <PERSON>a, <PERSON> } from '@storybook/addon-docs/blocks';

import * as ComponentStories from './course-sidebar-components.stories';

<Meta of={ComponentStories} />

# Course Sidebar Components

This page showcases the individual components that make up the Course Sidebar implementation.

## Chapter Item

The `SidebarChapterItemShadcn` component represents a chapter in the course, with an expandable list of lessons.

<Canvas of={ComponentStories.ChapterItem} />

### Features

- Expandable/collapsible display of lessons
- Chapter title with visual indicators
- Optional dropdown menu for actions (when editable)
- Consistent styling with rounded borders

## Lesson Item

The `SidebarLessonItemShadcn` component represents a lesson within a chapter or a standalone lesson.

<Canvas of={ComponentStories.LessonItem} />

### Features

- Visual indicators for lesson type (video, text, exercise)
- Status badges (draft, published)
- Quiz indicator when applicable
- Active state highlighting
- Optional dropdown menu for actions (when editable)

## Sidebar Toggle

The toggle component used to expand and collapse the sidebar.

<Canvas of={ComponentStories.SidebarToggleComponent} />

### Features

- Customizable appearance through children prop
- Function-as-children pattern for dynamic rendering based on state
- Default button with text for clear usage

## Complete Sidebar

The fully assembled course sidebar with all components working together.

<Canvas of={ComponentStories.CompleteSidebar} />

### Features

- Hierarchical navigation of course content
- Chapter and lesson organization
- Consistent styling throughout
- Full editing capabilities

## View-Only Mode

The sidebar in view-only mode, without editing capabilities.

<Canvas of={ComponentStories.CompleteSidebarViewOnly} />

## Collapsed Mode

The sidebar in its collapsed state, showing minimal information.

<Canvas of={ComponentStories.CompleteSidebarCollapsed} />

## Implementation Notes

These components are specifically designed for course navigation and management. The components handle:

1. **State Management**: Tracking expanded chapters and selected lessons
2. **Permission Control**: Showing/hiding edit controls based on user permissions
3. **Visual Hierarchy**: Clear distinction between chapters and lessons
4. **Consistent Styling**: Uniform borders, spacing, and visual indicators
