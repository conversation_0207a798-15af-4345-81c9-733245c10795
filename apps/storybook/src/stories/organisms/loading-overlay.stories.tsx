import type { Meta, StoryFn } from '@storybook/nextjs';

import { LoadingOverlay } from '@kit/ui/loading-overlay';

const meta: Meta<typeof LoadingOverlay> = {
  title: 'Organisms/Loading Overlay',
  component: LoadingOverlay,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

// Default loading overlay with full page coverage
export const Default: StoryFn<typeof LoadingOverlay> = () => (
  <LoadingOverlay>Loading...</LoadingOverlay>
);

// Loading overlay without full page coverage
export const Inline: StoryFn<typeof LoadingOverlay> = () => (
  <LoadingOverlay fullPage={false}>Processing your request...</LoadingOverlay>
);

// Loading overlay with custom spinner styling
export const CustomSpinner: StoryFn<typeof LoadingOverlay> = () => (
  <LoadingOverlay
    fullPage={false}
    spinnerClassName="h-12 w-12 text-blue-500"
    className="rounded-lg border p-8"
  >
    Customized loading state...
  </LoadingOverlay>
);

// Loading overlay without text content
export const SpinnerOnly: StoryFn<typeof LoadingOverlay> = () => (
  <LoadingOverlay fullPage={false} />
);
