import { Meta, StoryObj } from '@storybook/nextjs';

import { UserCommunities } from '@kit/ui/dojo/organisms/user-communities';
import type { UserCommunity } from '@kit/ui/dojo/organisms/user-communities';

const meta: Meta<typeof UserCommunities> = {
  title: 'Organisms/UserCommunities',
  component: UserCommunities,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof UserCommunities>;

const mockCommunities: UserCommunity[] = [
  {
    id: 'comm-1',
    name: 'Awesome Community 1',
    slug: 'awesome-community-1',
    logoUrl: 'https://via.placeholder.com/150/FF0000/FFFFFF?text=C1',
    coverUrl: 'https://via.placeholder.com/600x200/FF0000/FFFFFF?text=Cover1',
    description: 'This is the first awesome community.',
    isPrivate: false,
    memberCount: 150,
    categoryName: 'Tech',
    categoryIcon: '💻',
    languageName: 'English',
    languageIcon: '🇬🇧',
  },
  {
    id: 'comm-2',
    name: 'Creative Hub',
    slug: 'creative-hub',
    logoUrl: 'https://via.placeholder.com/150/00FF00/FFFFFF?text=C2',
    coverUrl: 'https://via.placeholder.com/600x200/00FF00/FFFFFF?text=Cover2',
    description: 'A place for creative minds.',
    isPrivate: true,
    memberCount: 75,
    categoryName: 'Arts',
    categoryIcon: '🎨',
    languageName: 'Spanish',
    languageIcon: '🇪🇸',
  },
  {
    id: 'comm-3',
    name: 'Gamer Zone',
    slug: 'gamer-zone',
    logoUrl: 'https://via.placeholder.com/150/0000FF/FFFFFF?text=C3',
    coverUrl: 'https://via.placeholder.com/600x200/0000FF/FFFFFF?text=Cover3',
    description: 'Community for gamers.',
    isPrivate: false,
    memberCount: 300,
    categoryName: 'Gaming',
    categoryIcon: '🎮',
    languageName: 'English',
    languageIcon: '🇬🇧',
  },
  {
    id: 'comm-4',
    name: 'Book Worms',
    slug: 'book-worms',
    logoUrl: 'https://via.placeholder.com/150/FFFF00/000000?text=C4',
    coverUrl: 'https://via.placeholder.com/600x200/FFFF00/000000?text=Cover4',
    description: 'For lovers of reading.',
    isPrivate: false,
    memberCount: 120,
    categoryName: 'Literature',
    categoryIcon: '📚',
    languageName: 'French',
    languageIcon: '🇫🇷',
  },
];

export const Default: Story = {
  args: {
    communities: mockCommunities,
    total: mockCommunities.length,
    isLoading: false,
    type: 'created',
  },
};

export const Member: Story = {
  name: 'Member (Not Owned)',
  args: {
    communities: mockCommunities,
    total: mockCommunities.length,
    isLoading: false,
    type: 'member',
  },
};

export const Loading: Story = {
  args: {
    communities: [],
    total: 0,
    isLoading: true,
    type: 'created',
  },
};

export const EmptyCreated: Story = {
  args: {
    communities: [],
    total: 0,
    isLoading: false,
    type: 'created',
  },
};

export const EmptyMember: Story = {
  name: 'Empty Member (Not Owned)',
  args: {
    communities: [],
    total: 0,
    isLoading: false,
    type: 'member',
  },
};
