import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as MultiStepFormStories from './multi-step-form.stories';

<Meta of={MultiStepFormStories} />

# Multi Step Form

A flexible and accessible multi-step form component that handles form state, validation, and navigation between steps.

## Features

- Step-by-step form progression with validation
- Form state management using React Hook Form
- Schema validation using Zod
- Animated transitions between steps
- Accessible keyboard navigation
- Progress tracking and indicators
- Customizable header and footer components
- Support for custom step content
- Dark mode compatible

## Usage

The MultiStepForm component requires a Zod schema, a form instance from React Hook Form, and step components.

```tsx
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  MultiStepForm,
  MultiStepFormFooter,
  MultiStepFormHeader,
  MultiStepFormStep,
  createStepSchema,
} from '@kit/ui/multi-step-form';

// Define your form schema using createStepSchema
const FormSchema = createStepSchema({
  step1: z.object({
    field1: z.string(),
    field2: z.string(),
  }),
  step2: z.object({
    field3: z.string(),
    field4: z.string(),
  }),
});

function MyForm() {
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      step1: { field1: '', field2: '' },
      step2: { field3: '', field4: '' },
    },
  });

  return (
    <MultiStepForm
      schema={FormSchema}
      form={form}
      onSubmit={(data) => console.log(data)}
    >
      <MultiStepFormHeader>
        <h2>My Form</h2>
      </MultiStepFormHeader>

      <MultiStepFormStep name="step1">{/* Step 1 fields */}</MultiStepFormStep>

      <MultiStepFormStep name="step2">{/* Step 2 fields */}</MultiStepFormStep>

      <MultiStepFormFooter>{/* Navigation buttons */}</MultiStepFormFooter>
    </MultiStepForm>
  );
}
```

## Examples

### Default Multi-Step Form

A basic multi-step form with three steps: personal information, contact information, and account setup.

<Canvas of={MultiStepFormStories.Default} />

### With Progress Indicator

A multi-step form that includes a progress bar and step counter.

<Canvas of={MultiStepFormStories.WithProgress} />

## Component API

### MultiStepForm

The main container component that manages the form state and step navigation.

<Controls of={MultiStepFormStories.Default} />

#### Props

- `schema`: Zod schema created using `createStepSchema`
- `form`: React Hook Form instance
- `onSubmit`: Callback function called when the form is submitted
- `useStepTransition`: Enable/disable step transition animations
- `className`: Additional CSS classes
- `children`: Form content (steps, header, footer)

### MultiStepFormStep

Component for individual form steps.

#### Props

- `name`: Step identifier (must match schema key)
- `children`: Step content
- `asChild`: Use child as root element
- `className`: Additional CSS classes

### MultiStepFormHeader

Optional header component for the form.

#### Props

- `children`: Header content
- `asChild`: Use child as root element
- `className`: Additional CSS classes

### MultiStepFormFooter

Optional footer component for navigation controls.

#### Props

- `children`: Footer content
- `asChild`: Use child as root element
- `className`: Additional CSS classes

## Accessibility

The Multi Step Form component follows WAI-ARIA guidelines for forms and dialogs:

- Proper focus management between steps
- ARIA labels and descriptions for form controls
- Keyboard navigation support
- Error announcements for screen readers
- Progressive enhancement for non-JS environments

## Design Guidelines

1. Keep steps focused and concise
2. Show clear progress indication
3. Provide clear error messages
4. Use consistent button placement
5. Maintain visual hierarchy
6. Support keyboard navigation
7. Include step descriptions
8. Show validation status

## Best Practices

1. Divide complex forms into logical steps
2. Validate each step before proceeding
3. Allow users to review all steps before submission
4. Provide clear navigation between steps
5. Save form progress when possible
6. Show validation errors immediately
7. Include progress indicators
8. Use clear and concise labels

## Technical Details

### Dependencies

- React Hook Form for form state management
- Zod for schema validation
- Radix UI primitives for accessibility
- Tailwind CSS for styling

### Performance Considerations

- Lazy loading of step content
- Optimized step transitions
- Efficient form state updates
- Minimal re-renders

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Progressive enhancement for older browsers
- Fallback for disabled JavaScript

## Related Components

- Form (`@kit/ui/form`)
- Input (`@kit/ui/input`)
- Button (`@kit/ui/button`)
- Stepper (`@kit/ui/stepper`)
