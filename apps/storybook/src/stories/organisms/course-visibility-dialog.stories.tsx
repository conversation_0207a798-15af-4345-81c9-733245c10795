import type { Meta, StoryObj } from '@storybook/nextjs';
import { Lock } from 'lucide-react';

import {
  CourseVisibilityDialog,
  type CourseVisibilityInfo,
} from '@kit/ui/dojo/organisms/course-visibility-dialog';

const meta = {
  title: 'Course/Organisms/CourseVisibilityDialog',
  component: CourseVisibilityDialog,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseVisibilityDialog>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultCourse: CourseVisibilityInfo = {
  id: '1',
  slug: 'react-fundamentals',
  title: 'React Fundamentals',
  description: 'Learn the basics of React development',
  coverUrl: 'https://picsum.photos/400/300',
  status: 'published',
  lessons: 12,
  durationSeconds: 7200,
};

export const Default: Story = {
  args: {
    course: defaultCourse,
    text: 'Private Course',
    description: 'This course is private',
    icon: <Lock className="mb-2 h-8 w-8" />,
    isOpen: true,
    onOpenChange: () => {},
  },
};
