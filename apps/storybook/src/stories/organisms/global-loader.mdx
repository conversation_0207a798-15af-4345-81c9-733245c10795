import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as GlobalLoaderStories from './global-loader.stories';

<Meta of={GlobalLoaderStories} />

# Global Loader

The Global Loader is a full-screen loading indicator component that provides visual feedback during application-wide loading states. It's built using Shadcn UI components and follows the application's theme.

## Features

- Full-screen overlay
- Centered loading spinner
- Customizable loading message
- Smooth transitions
- Theme-aware styling
- Accessibility support

## Usage

```tsx
import { GlobalLoader } from '@kit/ui/global-loader';

// Basic usage
export function MyApp() {
  return (
    <div>
      <GlobalLoader />
      {/* Your app content */}
    </div>
  );
}

// With custom message
export function MyAppWithMessage() {
  return (
    <div>
      <GlobalLoader message="Loading your dashboard..." />
      {/* Your app content */}
    </div>
  );
}
```

## Examples

### Default

<Canvas of={GlobalLoaderStories.Default} />

### Spinner Only

<Canvas of={GlobalLoaderStories.SpinnerOnly} />

### Top Loading Bar Only

<Canvas of={GlobalLoaderStories.TopLoadingBarOnly} />

### With Custom Content

<Canvas of={GlobalLoaderStories.WithCustomContent} />

## Component API

<Controls />

## Props

| Prop          | Type                   | Default        | Description                 |
| ------------- | ---------------------- | -------------- | --------------------------- |
| `message`     | `string`               | `'Loading...'` | Custom loading message      |
| `isVisible`   | `boolean`              | `true`         | Control loader visibility   |
| `className`   | `string`               | -              | Additional CSS classes      |
| `spinnerSize` | `'sm' \| 'md' \| 'lg'` | `'md'`         | Size of the loading spinner |

## Accessibility

The Global Loader implements accessibility best practices:

- Uses `aria-live="polite"` for screen reader announcements
- Manages focus appropriately during loading states
- Provides proper ARIA attributes for the loading spinner
- Ensures keyboard navigation is blocked while loading

## Theming

The component inherits from your application's theme and uses Shadcn UI's theming system:

```tsx
// Default styling
<div className="bg-background/80 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-xs">
  <div className="flex flex-col items-center space-y-4">
    <Spinner className="text-primary h-8 w-8" />
    <p className="text-muted-foreground text-sm">{message}</p>
  </div>
</div>
```

## Best Practices

1. Use for application-wide loading states only
2. Keep loading messages concise and informative
3. Consider using skeleton loaders for component-level loading
4. Implement timeouts for long-running operations
5. Provide error states for failed operations
