import type { Meta, StoryObj } from '@storybook/nextjs';

import { TipTapEditor } from '@kit/ui/dojo/organisms/tiptap-editor';

const meta = {
  title: 'Organisms/TipTapEditor',
  component: TipTapEditor,
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => (
      <div className="bg-background p-4">
        <Story />
      </div>
    ),
  ],
  args: {
    className: 'w-[800px] min-h-[400px] border rounded-lg',
    editorContentClassName: 'prose dark:prose-invert max-w-none p-4',
    placeholder: 'Start typing...',
    onImageUpload: async (file: File) => {
      // Mock image upload - in real app this would upload to storage
      console.log('Mock image upload:', file.name);
      return URL.createObjectURL(file);
    },
    output: 'json',
    contextType: 'forumPost',
  },
} satisfies Meta<typeof TipTapEditor>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithContent: Story = {
  args: {
    value: {
      type: 'doc',
      content: [
        {
          type: 'heading',
          attrs: { level: 1 },
          content: [{ type: 'text', text: 'Hello World' }],
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'This is a test' }],
        },
      ],
    },
  },
};
