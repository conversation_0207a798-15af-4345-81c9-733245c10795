import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as SidebarStories from './sidebar.stories';

<Meta of={SidebarStories} />

# Sidebar

A responsive and customizable sidebar component for navigation, featuring collapsible groups, icons, and hover interactions. Built with accessibility in mind, it provides a robust navigation solution for web applications.

## Features

- Collapsible navigation with smooth transitions
- Expand on hover functionality for better UX
- Grouped navigation items with optional labels
- Icon support with consistent sizing
- Responsive design with mobile considerations
- Dark mode compatible using CSS variables
- Keyboard navigation and focus management
- Automatic ARIA attributes for accessibility
- Focus management for keyboard users
- Customizable through CSS variables

## Installation

```bash
import { Sidebar } from "@kit/ui/sidebar"
```

## Usage

```tsx
import { Home } from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarDivider,
  SidebarGroup,
  SidebarItem,
} from '@kit/ui/sidebar';

export function MyNavigation() {
  return (
    <Sidebar>
      <div className="p-4">
        <h2 className="text-lg font-semibold">My App</h2>
      </div>
      <SidebarContent>
        <SidebarGroup label="Main Menu">
          <SidebarItem path="/home" Icon={<Home className="h-4 w-4" />}>
            Home
          </SidebarItem>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
```

## Examples

### Default

A basic sidebar with grouped navigation items.

<Canvas of={SidebarStories.Default} height={400} />

### With Divider

Using dividers to separate different navigation groups.

<Canvas of={SidebarStories.WithDivider} height={400} />

### Collapsed

Sidebar in its collapsed state, showing only icons.

<Canvas of={SidebarStories.Collapsed} height={400} />

### Expand on Hover

Collapsed sidebar that expands when hovered over.

<Canvas of={SidebarStories.ExpandOnHover} height={400} />

## Props

### Sidebar

<Controls />

### SidebarContent

Container for sidebar content with proper spacing and layout.

- `className`: Additional CSS classes
- `children`: React nodes to render inside the content area

### SidebarGroup

Groups related navigation items with an optional label.

- `label`: Text label for the group
- `collapsible`: Whether the group can be collapsed (default: false)
- `collapsed`: Initial collapsed state (default: false)
- `className`: Additional CSS classes
- `children`: Navigation items to render in the group

### SidebarItem

Individual navigation item with icon support.

- `path`: Navigation path (required)
- `Icon`: React element for the icon
- `end`: Route matching strategy (default: false)
- `className`: Additional CSS classes
- `children`: Label text for the item

### SidebarDivider

A horizontal line to separate groups of navigation items.

- `className`: Additional CSS classes

## CSS

### Global Styles

```css
.sidebar {
  /* Base styles */
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --sidebar-background: hsl(var(--background));
  --sidebar-foreground: hsl(var(--foreground));

  /* Animation */
  --sidebar-transition: width 200ms ease-in-out;
}
```

### Custom Styles

```css
.sidebar {
  /* Customize width */
  --sidebar-width: 280px;

  /* Customize colors */
  --sidebar-background: hsl(var(--slate-950));
  --sidebar-foreground: hsl(var(--slate-50));
}
```

## Accessibility

The Sidebar component follows WAI-ARIA guidelines for navigation landmarks and menus.

### Keyboard Interactions

- `Tab`: Navigate through menu items
- `Enter`/`Space`: Activate the current menu item
- `Arrow Up`/`Arrow Down`: Navigate between menu items
- `Arrow Left`/`Arrow Right`: Collapse/expand groups
- `Home`/`End`: Jump to first/last menu item
- `Escape`: Collapse expanded groups

### Screen Readers

- Uses semantic HTML elements
- Implements proper ARIA roles and attributes
- Provides descriptive labels for icons
- Announces state changes
- Manages focus appropriately

## Guidelines

### Best Practices

1. Structure

   - Keep navigation hierarchy shallow
   - Group related items logically
   - Use clear, concise labels
   - Include visual indicators for active states
   - Consider mobile breakpoints

2. Content

   - Use consistent icon sizes
   - Provide meaningful labels
   - Keep group sizes manageable
   - Include visual feedback
   - Consider loading states

3. Accessibility
   - Test keyboard navigation
   - Verify screen reader compatibility
   - Ensure sufficient color contrast
   - Provide text alternatives for icons
   - Maintain focus management

### Performance

1. Optimization

   - Lazy load nested content
   - Minimize state updates
   - Use CSS transitions
   - Implement proper memoization
   - Monitor render cycles

2. Responsiveness
   - Handle window resizing
   - Implement breakpoints
   - Consider touch interfaces
   - Test on various devices
   - Optimize for mobile

## API Reference

### Types

```tsx
interface SidebarProps {
  collapsed?: boolean;
  expandOnHover?: boolean;
  className?: string;
  children: React.ReactNode;
}

interface SidebarGroupProps {
  label?: string;
  collapsible?: boolean;
  collapsed?: boolean;
  className?: string;
  children: React.ReactNode;
}

interface SidebarItemProps {
  path: string;
  Icon?: React.ReactNode;
  end?: boolean;
  className?: string;
  children: React.ReactNode;
}
```

### State Management

The Sidebar manages several internal states:

- Collapsed state
- Hover expansion
- Active item
- Group expansion
- Focus management

These states are handled automatically but can be controlled through props when needed.
