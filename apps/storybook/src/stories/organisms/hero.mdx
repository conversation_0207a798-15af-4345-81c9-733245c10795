import { Can<PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as HeroStories from './hero.stories';

<Meta of={HeroStories} />

# Hero

The Hero component is a versatile and impactful landing section designed for showcasing key messages and content at the top of your pages.

## Features

- Animated entrance effects
- Support for feature announcement pills
- Flexible CTA section
- Optional hero image
- Responsive design
- Dark mode compatible

## Usage

```tsx
import { Hero } from '@kit/ui/hero';

export default function Page() {
  return (
    <Hero
      pill={<AnnouncementPill />}
      title="Your Headline"
      subtitle="Your subtitle or description"
      cta={<CTAButtons />}
      image={<HeroImage />}
    />
  );
}
```

## Examples

### Default Hero

A standard hero section with a pill, title, subtitle, and CTA buttons.

<Canvas of={HeroStories.Default} />

### Hero with Image

A hero section that includes a featured image below the content.

<Canvas of={HeroStories.WithImage} />

### Without Animation

A hero section with animations disabled.

<Canvas of={HeroStories.WithoutAnimation} />

### Minimal Hero

A simplified hero with just title and subtitle.

<Canvas of={HeroStories.Minimal} />

## Component API

<Controls />

## Accessibility

The Hero component follows accessibility best practices:

- Semantic HTML structure
- Proper heading hierarchy
- ARIA attributes where necessary
- Keyboard navigation support
- High contrast text colors

## Best Practices

1. Keep titles concise and impactful
2. Use clear and actionable CTA text
3. Ensure image alt text is descriptive
4. Test responsiveness across devices
5. Consider load time when using images
