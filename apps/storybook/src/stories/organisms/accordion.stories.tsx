'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Plus } from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@kit/ui/accordion';

const meta = {
  title: 'Organisms/Accordion',
  component: Accordion,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['single', 'multiple'],
      description: 'Whether one or multiple items can be opened at once',
    },
    collapsible: {
      control: 'boolean',
      description: 'Whether the opened item can be closed',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Accordion>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    type: 'single',
    collapsible: true,
  },
  render: (args) => (
    <Accordion {...args} className="w-[450px]">
      <AccordionItem value="item-1">
        <AccordionTrigger>Is it accessible?</AccordionTrigger>
        <AccordionContent>
          Yes. It adheres to the WAI-ARIA design pattern.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-2">
        <AccordionTrigger>Is it styled?</AccordionTrigger>
        <AccordionContent>
          Yes. It comes with default styles that match your theme.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-3">
        <AccordionTrigger>Is it animated?</AccordionTrigger>
        <AccordionContent>
          Yes. It&apos;s animated by default, but you can disable it if you
          prefer.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const Multiple: Story = {
  args: {
    type: 'multiple',
  },
  render: (args) => (
    <Accordion {...args} className="w-[450px]">
      <AccordionItem value="item-1">
        <AccordionTrigger>First Section</AccordionTrigger>
        <AccordionContent>
          You can open multiple sections at the same time.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-2">
        <AccordionTrigger>Second Section</AccordionTrigger>
        <AccordionContent>Try opening another section.</AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-3">
        <AccordionTrigger>Third Section</AccordionTrigger>
        <AccordionContent>All sections can be open at once.</AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const WithCustomTrigger: Story = {
  args: {
    type: 'single',
    collapsible: true,
  },
  render: (args) => (
    <Accordion {...args} className="w-[450px]">
      <AccordionItem value="item-1">
        <AccordionTrigger className="hover:no-underline">
          <div className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            <span>Custom Trigger Style</span>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          This trigger has a custom style with an icon.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem value="item-2">
        <AccordionTrigger className="text-primary hover:no-underline">
          Colored Trigger
        </AccordionTrigger>
        <AccordionContent>This trigger has a custom color.</AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const Nested: Story = {
  args: {
    type: 'single',
    collapsible: true,
  },
  render: (args) => (
    <Accordion {...args} className="w-[450px]">
      <AccordionItem value="item-1">
        <AccordionTrigger>Main Section</AccordionTrigger>
        <AccordionContent>
          <Accordion type="single" collapsible>
            <AccordionItem value="nested-1">
              <AccordionTrigger>Nested Section 1</AccordionTrigger>
              <AccordionContent>First nested content.</AccordionContent>
            </AccordionItem>
            <AccordionItem value="nested-2">
              <AccordionTrigger>Nested Section 2</AccordionTrigger>
              <AccordionContent>Second nested content.</AccordionContent>
            </AccordionItem>
          </Accordion>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};

export const WithLongContent: Story = {
  args: {
    type: 'single',
    collapsible: true,
  },
  render: (args) => (
    <Accordion {...args} className="w-[450px]">
      <AccordionItem value="item-1">
        <AccordionTrigger>Detailed Information</AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <p>
              This is an example of an accordion item with longer content. The
              content area smoothly animates to accommodate the content length.
            </p>
            <ul className="text-muted-foreground list-disc pl-4">
              <li>First important point about the topic</li>
              <li>Second point with additional details</li>
              <li>Third point explaining more features</li>
            </ul>
            <p>
              The accordion handles this content gracefully while maintaining
              smooth animations and proper spacing.
            </p>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  ),
};
