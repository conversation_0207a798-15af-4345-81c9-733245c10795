import type { Meta, StoryFn } from '@storybook/nextjs';

import { Stepper } from '@kit/ui/stepper';

const meta: Meta<typeof Stepper> = {
  title: 'Organisms/Stepper',
  component: Stepper,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

// Default stepper with numbers variant
export const Default: StoryFn<typeof Stepper> = () => {
  const steps = ['Personal Info', 'Contact Details', 'Review'];
  return <Stepper steps={steps} currentStep={1} variant="numbers" />;
};

// Dots variant
export const Dots: StoryFn<typeof Stepper> = () => {
  const steps = ['Step 1', 'Step 2', 'Step 3', 'Step 4'];
  return <Stepper steps={steps} currentStep={2} variant="dots" />;
};

// Default variant with progress bar
export const ProgressBar: StoryFn<typeof Stepper> = () => {
  const steps = ['Upload', 'Process', 'Verify', 'Complete'];
  return <Stepper steps={steps} currentStep={1} variant="default" />;
};

// Completed steps example
export const CompletedSteps: StoryFn<typeof Stepper> = () => {
  const steps = ['Account', 'Profile', 'Settings', 'Finish'];
  return <Stepper steps={steps} currentStep={3} variant="numbers" />;
};

// First step example
export const FirstStep: StoryFn<typeof Stepper> = () => {
  const steps = ['Start', 'Configure', 'Deploy'];
  return <Stepper steps={steps} currentStep={0} variant="numbers" />;
};

// Last step example
export const LastStep: StoryFn<typeof Stepper> = () => {
  const steps = ['Plan', 'Build', 'Review', 'Launch'];
  return <Stepper steps={steps} currentStep={3} variant="numbers" />;
};
