import type { Meta, StoryObj } from '@storybook/nextjs';

import { CommunityCard } from '@kit/ui/dojo/organisms/community-card';

const meta = {
  title: 'Organisms/CommunityCard',
  component: CommunityCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CommunityCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'JavaScript Mastery',
    description:
      'Learn JavaScript from beginner to advanced. Join our community of developers and level up your skills.',
    pictureUrl: 'https://picsum.photos/200',
    coverImage: 'https://picsum.photos/800/400',
    slug: 'javascript-mastery',
    memberCount: 1234,
    isPrivate: false,
    feeAmount: 0,
    categoryName: 'programming',
    categoryIcon: '💻',
    languageName: 'english',
    languageIcon: '🇬🇧',
  },
};

export const Private: Story = {
  args: {
    ...Default.args,
    name: 'Advanced TypeScript',
    isPrivate: true,
    feeAmount: 29.99,
  },
};

export const NoImages: Story = {
  args: {
    ...Default.args,
    pictureUrl: '',
    coverImage: '',
  },
};

export const LongContent: Story = {
  args: {
    ...Default.args,
    name: 'Advanced Machine Learning and Artificial Intelligence Study Group with Focus on Neural Networks',
    description:
      'This is a very long description that should be truncated. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
  },
};

export const WithoutFooter: Story = {
  args: {
    ...Default.args,
    showFooter: false,
  },
};

export const WithoutCategory: Story = {
  args: {
    ...Default.args,
    showCategory: false,
  },
};

export const WithoutLanguage: Story = {
  args: {
    ...Default.args,
    showLanguage: false,
  },
};

export const HighMemberCount: Story = {
  args: {
    ...Default.args,
    memberCount: 1000000,
  },
};

export const RTLContent: Story = {
  args: {
    ...Default.args,
    name: 'تعلم البرمجة',
    description: 'مجتمع لتعلم البرمجة باللغة العربية',
    languageName: 'arabic',
    languageIcon: '🇸🇦',
  },
};

export const EmojiContent: Story = {
  args: {
    ...Default.args,
    name: '🎯 Target Practice 🎯',
    description:
      '🎯 Learn archery from the best! 🏹 Join our community of archers and improve your skills! 🎪',
    categoryIcon: '🎯',
    languageIcon: '🌍',
  },
};
