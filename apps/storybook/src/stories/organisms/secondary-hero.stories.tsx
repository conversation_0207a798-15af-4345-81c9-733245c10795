import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Zap } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { SecondaryHero } from '@kit/ui/dojo/organisms/secondary-hero';

const meta = {
  title: 'Organisms/Secondary Hero',
  component: SecondaryHero,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof SecondaryHero>;

export default meta;
type Story = StoryObj<typeof SecondaryHero>;

const Pill = () => (
  <div className="bg-muted inline-flex items-center rounded-full border px-3 py-1 text-sm">
    <Zap className="mr-2 h-3 w-3" />
    New Features
  </div>
);

export const Default: Story = {
  args: {
    heading: 'Discover Our Platform',
    subheading: 'Everything you need to build amazing products',
    children: (
      <div className="mt-6 flex justify-center gap-4">
        <Button size="lg">Get Started</Button>
        <Button size="lg" variant="outline">
          Learn More
        </Button>
      </div>
    ),
  },
};

export const WithPill: Story = {
  args: {
    pill: <Pill />,
    heading: 'Introducing New Features',
    subheading: 'Enhance your workflow with our latest updates',
    children: (
      <div className="mt-6 flex justify-center gap-4">
        <Button size="lg">Explore Now</Button>
      </div>
    ),
  },
};

export const WithCustomStyling: Story = {
  args: {
    className: 'max-w-3xl bg-muted/50 p-12 rounded-2xl',
    heading: 'Join Our Community',
    subheading: 'Connect with developers from around the world',
    children: (
      <div className="mt-8 flex flex-col items-center gap-6">
        <p className="text-muted-foreground max-w-xl text-center">
          Join thousands of developers who are already building the future with
          our platform.
        </p>
        <Button size="lg" className="bg-primary hover:bg-primary/90">
          Join Now
        </Button>
      </div>
    ),
  },
};
