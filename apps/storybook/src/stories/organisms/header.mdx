import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as HeaderStories from './header.stories';

<Meta of={HeaderStories} />

# Header

The Header component is a versatile navigation component designed for the top of your application, featuring a logo, navigation links, and action buttons.

## Features

- Sticky positioning with backdrop blur
- Responsive design
- Dark mode compatible
- Flexible layout with logo, navigation, and actions sections
- Mobile-friendly with collapsible navigation
- Customizable styling

## Usage

```tsx
import { Header } from '@kit/ui/header';

export default function Page() {
  return (
    <Header
      logo={<YourLogo />}
      navigation={<YourNavigation />}
      actions={<YourActions />}
    />
  );
}
```

## Examples

### Default Header

A complete header with logo, navigation, and action buttons.

<Canvas of={HeaderStories.Default} />

### Without Navigation

A simplified header with just logo and actions.

<Canvas of={HeaderStories.WithoutNavigation} />

### Without Actions

A header focused on navigation without action buttons.

<Canvas of={HeaderStories.WithoutActions} />

### Logo Only

A minimal header displaying only the logo.

<Canvas of={HeaderStories.LogoOnly} />

## Component API

<Controls />

## Accessibility

The Header component follows accessibility best practices:

- Semantic HTML structure
- Proper navigation landmarks
- Keyboard navigation support
- ARIA attributes for interactive elements
- High contrast text colors
- Focus management for navigation items

## Best Practices

1. Keep navigation items concise and clear
2. Use descriptive labels for buttons and links
3. Ensure logo has appropriate alt text
4. Test navigation on all screen sizes
5. Consider mobile navigation patterns
6. Maintain consistent spacing between elements

## Customization

The Header component can be customized using className props:

```tsx
<Header
  className="custom-header-class"
  logo={<Logo className="custom-logo-class" />}
  navigation={<nav className="custom-nav-class">...</nav>}
  actions={<div className="custom-actions-class">...</div>}
/>
```
