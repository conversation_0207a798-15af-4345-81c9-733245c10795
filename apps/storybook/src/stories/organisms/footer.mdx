import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as FooterStories from './footer.stories';

<Meta of={FooterStories} />

# Footer

The Footer component is a comprehensive site footer that includes a logo, description, copyright notice, and multiple sections of links organized by category.

## Features

- Responsive layout
- Dark mode compatible
- Customizable sections
- Flexible content areas
- Social media integration
- Semantic HTML structure

## Usage

```tsx
import { Footer } from '@kit/ui/footer';

export default function Page() {
  return (
    <Footer
      logo={<YourLogo />}
      description="Your company description"
      copyright="Your copyright notice"
      sections={[
        {
          heading: 'Section Title',
          links: [
            { href: '/link1', label: 'Link 1' },
            { href: '/link2', label: 'Link 2' },
          ],
        },
      ]}
    />
  );
}
```

## Examples

### Default Footer

A complete footer with all sections and default styling.

<Canvas of={FooterStories.Default} />

### With Fewer Sections

A simplified footer with only two sections.

<Canvas of={FooterStories.WithFewerSections} />

### With Custom Description

A footer featuring a custom description area with social media links.

<Canvas of={FooterStories.WithCustomDescription} />

## Component API

<Controls />

## Accessibility

The Footer component follows accessibility best practices:

- Semantic HTML structure
- ARIA landmarks
- Proper heading hierarchy
- Keyboard navigation support
- High contrast text colors
- Focus management for interactive elements

## Best Practices

1. Keep section headings concise
2. Group related links logically
3. Ensure all links are functional
4. Include essential legal links
5. Maintain consistent spacing
6. Test responsive behavior
7. Consider mobile-first layout

## Customization

The Footer component can be customized using className props and by providing custom content:

```tsx
<Footer
  className="custom-footer-class"
  logo={<CustomLogo className="custom-logo-class" />}
  description={<CustomDescription />}
  copyright={<CustomCopyright />}
  sections={customSections}
/>
```

## Section Structure

Each section in the footer should follow this structure:

```typescript
interface FooterSection {
  heading: React.ReactNode;
  links: Array<{
    href: string;
    label: React.ReactNode;
  }>;
}
```

This allows for flexible content while maintaining consistent layout and styling.
