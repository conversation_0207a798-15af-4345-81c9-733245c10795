import { Canvas, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as ChartStories from './chart.stories';

<Meta of={ChartStories} />

# Chart

The Chart component is a flexible data visualization component built on top of Recharts, providing a consistent theme-aware charting solution. It supports various chart types and comes with built-in tooltips and legends.

## Features

- Multiple chart types (Line, Bar, Area, Pie)
- Theme-aware styling
- Customizable tooltips and legends
- Responsive design
- Dark mode support
- Accessible data visualization

## Usage

The Chart component is designed to be used with different types of charts from Recharts. It provides a consistent theming and styling experience across all chart types.

### Line Chart

Line charts are perfect for showing trends over time or continuous data series.

<Canvas of={ChartStories.LineChartExample} />

```tsx
<ChartContainer config={chartConfig}>
  <LineChart data={data}>
    <Line
      type="monotone"
      dataKey="value"
      name="sales"
      stroke="var(--color-sales)"
      strokeWidth={2}
    />
    <ChartTooltip content={<ChartTooltipContent />} />
    <ChartLegend content={<ChartLegendContent />} />
  </LineChart>
</ChartContainer>
```

### Bar Chart

Bar charts are ideal for comparing quantities across different categories.

<Canvas of={ChartStories.BarChartExample} />

```tsx
<ChartContainer config={chartConfig}>
  <BarChart data={data}>
    <Bar dataKey="value" name="sales" fill="var(--color-sales)" />
    <ChartTooltip content={<ChartTooltipContent />} />
    <ChartLegend content={<ChartLegendContent />} />
  </BarChart>
</ChartContainer>
```

### Area Chart

Area charts are useful for showing volume over time and can be stacked to show cumulative values.

<Canvas of={ChartStories.AreaChartExample} />

```tsx
<ChartContainer config={chartConfig}>
  <AreaChart data={data}>
    <Area
      type="monotone"
      dataKey="value"
      name="sales"
      fill="var(--color-sales)"
      fillOpacity={0.2}
      stroke="var(--color-sales)"
    />
    <ChartTooltip content={<ChartTooltipContent />} />
    <ChartLegend content={<ChartLegendContent />} />
  </AreaChart>
</ChartContainer>
```

### Pie Chart

Pie charts are effective for showing proportional data or part-to-whole relationships.

<Canvas of={ChartStories.PieChartExample} />

```tsx
<ChartContainer config={chartConfig}>
  <PieChart>
    <Pie
      data={data}
      dataKey="value"
      nameKey="name"
      cx="50%"
      cy="50%"
      fill="var(--color-sales)"
    />
    <ChartTooltip content={<ChartTooltipContent />} />
    <ChartLegend content={<ChartLegendContent />} />
  </PieChart>
</ChartContainer>
```

## Component API

### ChartContainer

The main wrapper component that provides theming and configuration context.

<Controls />

#### Props

- `config`: Chart configuration object for theming and labels
- `className`: Additional CSS classes
- `children`: Chart component from Recharts

### ChartTooltip & ChartTooltipContent

Components for displaying data tooltips on hover.

```tsx
<ChartTooltip content={<ChartTooltipContent />} />
```

#### ChartTooltipContent Props

- `hideLabel`: Hide the tooltip label
- `hideIndicator`: Hide the color indicator
- `indicator`: Type of indicator ('line' | 'dot' | 'dashed')
- `nameKey`: Key for the data point name
- `labelKey`: Key for the data point label

### ChartLegend & ChartLegendContent

Components for displaying the chart legend.

```tsx
<ChartLegend content={<ChartLegendContent />} />
```

#### ChartLegendContent Props

- `hideIcon`: Hide the legend icons
- `nameKey`: Key for the legend item name
- `verticalAlign`: Vertical alignment of the legend

## Configuration

The chart configuration object defines themes and labels for the data series:

```tsx
const chartConfig = {
  series1: {
    label: 'Series 1',
    theme: {
      light: 'hsl(var(--primary))',
      dark: 'hsl(var(--primary))',
    },
  },
  // ... more series configurations
};
```

## Accessibility

The Chart component implements accessibility features:

- Screen reader support for data points
- Keyboard navigation
- ARIA labels and descriptions
- Color contrast compliance
- Focus management

## Design Guidelines

- Choose the appropriate chart type for your data
- Use clear and consistent labeling
- Maintain proper spacing and sizing
- Consider mobile responsiveness
- Use appropriate color schemes for data visualization
- Provide clear context and legends

## Technical Details

- Built on top of Recharts library
- Theme-aware using CSS variables
- Responsive by default
- Optimized for performance
- Supports SSR
- Dark mode compatible

## Best Practices

1. Data Preparation

   - Clean and format data appropriately
   - Use consistent data structures
   - Handle missing or invalid data

2. Chart Selection

   - Use line charts for trends
   - Use bar charts for comparisons
   - Use area charts for volumes
   - Use pie charts for proportions

3. Styling

   - Maintain consistent colors
   - Use appropriate spacing
   - Consider mobile viewports
   - Follow brand guidelines

4. Performance
   - Limit data points when possible
   - Use appropriate update intervals
   - Implement proper memoization
   - Consider lazy loading for complex charts

## Related Components

- DataTable
- Stats
- Metrics
- Dashboard
- KPI
