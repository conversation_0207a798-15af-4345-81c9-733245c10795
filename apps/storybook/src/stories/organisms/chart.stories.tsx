import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import {
  type ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltipContent,
} from '@kit/ui/chart';

const meta = {
  title: 'Organisms/Chart',
  component: ChartContainer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    config: {
      control: 'object',
    },
  },
} satisfies Meta<typeof ChartContainer>;

export default meta;
type Story = StoryObj<typeof ChartContainer>;

// Sample data for different chart types
const lineData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 600 },
  { name: 'Apr', value: 800 },
  { name: 'May', value: 500 },
];

const barData = [
  { name: 'Product A', sales: 400 },
  { name: 'Product B', sales: 300 },
  { name: 'Product C', sales: 600 },
  { name: 'Product D', sales: 800 },
  { name: 'Product E', sales: 500 },
];

const areaData = [
  { name: 'Jan', value: 400, value2: 240 },
  { name: 'Feb', value: 300, value2: 139 },
  { name: 'Mar', value: 600, value2: 980 },
  { name: 'Apr', value: 800, value2: 390 },
  { name: 'May', value: 500, value2: 480 },
];

const pieData = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
];

// Chart configuration for theming
const chartConfig: ChartConfig = {
  value: {
    label: 'Value',
    theme: {
      light: '#8884d8',
      dark: '#8884d8',
    },
  },
  value2: {
    label: 'Value 2',
    theme: {
      light: '#82ca9d',
      dark: '#82ca9d',
    },
  },
  sales: {
    label: 'Sales',
    theme: {
      light: '#8884d8',
      dark: '#8884d8',
    },
  },
};

export const LineChartExample: Story = {
  args: {
    config: chartConfig,
    children: (
      <>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={lineData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<ChartTooltipContent />} />
            <Line type="monotone" dataKey="value" stroke="var(--color-value)" />
          </LineChart>
        </ResponsiveContainer>
        <ChartLegend>
          <ChartLegendContent />
        </ChartLegend>
      </>
    ),
  },
  render: (args) => (
    <div className="h-[400px] w-[600px]">
      <ChartContainer {...args} />
    </div>
  ),
};

export const BarChartExample: Story = {
  args: {
    config: chartConfig,
    children: (
      <>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={barData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<ChartTooltipContent />} />
            <Bar dataKey="sales" fill="var(--color-sales)" />
          </BarChart>
        </ResponsiveContainer>
        <ChartLegend>
          <ChartLegendContent />
        </ChartLegend>
      </>
    ),
  },
  render: (args) => (
    <div className="h-[400px] w-[600px]">
      <ChartContainer {...args} />
    </div>
  ),
};

export const AreaChartExample: Story = {
  args: {
    config: chartConfig,
    children: (
      <>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={areaData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<ChartTooltipContent />} />
            <Area
              type="monotone"
              dataKey="value"
              stackId="1"
              stroke="var(--color-value)"
              fill="var(--color-value)"
            />
            <Area
              type="monotone"
              dataKey="value2"
              stackId="1"
              stroke="var(--color-value2)"
              fill="var(--color-value2)"
            />
          </AreaChart>
        </ResponsiveContainer>
        <ChartLegend>
          <ChartLegendContent />
        </ChartLegend>
      </>
    ),
  },
  render: (args) => (
    <div className="h-[400px] w-[600px]">
      <ChartContainer {...args} />
    </div>
  ),
};

export const PieChartExample: Story = {
  args: {
    config: chartConfig,
    children: (
      <>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Tooltip content={<ChartTooltipContent />} />
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              outerRadius={80}
              fill="var(--color-value)"
              dataKey="value"
              label
            />
          </PieChart>
        </ResponsiveContainer>
        <ChartLegend>
          <ChartLegendContent />
        </ChartLegend>
      </>
    ),
  },
  render: (args) => (
    <div className="h-[400px] w-[600px]">
      <ChartContainer {...args} />
    </div>
  ),
};
