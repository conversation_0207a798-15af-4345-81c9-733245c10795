import type { Meta, StoryObj } from '@storybook/nextjs';

import { HeroTitle } from '@kit/ui/dojo/atoms/hero-title';

const meta = {
  title: 'Organisms/Hero Title',
  component: HeroTitle,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof HeroTitle>;

export default meta;
type Story = StoryObj<typeof HeroTitle>;

export const Default: Story = {
  args: {
    children: 'Build Something Amazing',
  },
};

export const WithHighlight: Story = {
  args: {
    children: (
      <>
        Build Something{' '}
        <span className="bg-linear-to-r from-pink-500 to-violet-500 bg-clip-text text-transparent">
          Amazing
        </span>
      </>
    ),
  },
};

export const Multiline: Story = {
  args: {
    children: (
      <>
        Build Something
        <br />
        Extraordinary
      </>
    ),
  },
};

export const WithCustomClassName: Story = {
  args: {
    className: 'text-primary',
    children: 'Custom Styled Title',
  },
};
