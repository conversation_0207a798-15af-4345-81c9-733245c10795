import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as SecondaryHeroStories from './secondary-hero.stories';

<Meta of={SecondaryHeroStories} />

# Secondary Hero

The Secondary Hero component is a versatile section component designed for secondary content areas, featuring a heading, subheading, optional pill, and customizable content area.

## Features

- Optional feature announcement pill
- Customizable heading and subheading
- Flexible content area
- Responsive design
- Dark mode compatible
- Centered layout

## Usage

```tsx
import { SecondaryHero } from '@kit/ui/secondary-hero';

export default function Page() {
  return (
    <SecondaryHero
      heading="Your Heading"
      subheading="Your subheading text"
      pill={<AnnouncementPill />}
    >
      <YourContent />
    </SecondaryHero>
  );
}
```

## Examples

### Default

A basic secondary hero section with heading, subheading, and call-to-action buttons.

<Canvas of={SecondaryHeroStories.Default} />

### With Pill

A secondary hero featuring an announcement pill above the heading.

<Canvas of={SecondaryHeroStories.WithPill} />

### With Custom Styling

Custom styling applied to the container and content.

<Canvas of={SecondaryHeroStories.WithCustomStyling} />

## Component API

<Controls />

## Accessibility

The Secondary Hero component follows accessibility best practices:

- Semantic HTML structure
- Proper heading hierarchy
- ARIA attributes where necessary
- High contrast text colors
- Keyboard navigation support

## Best Practices

1. Keep headings concise and impactful
2. Use clear and descriptive subheadings
3. Ensure pill text is readable
4. Maintain consistent spacing
5. Test responsive behavior
6. Consider content hierarchy

## Layout Structure

The component uses a centered layout with consistent spacing:

```tsx
<div className="flex flex-col items-center space-y-6 text-center">
  {pill}
  <div className="flex flex-col">
    <Heading>{heading}</Heading>
    <h3>{subheading}</h3>
  </div>
  {children}
</div>
```

## Customization

The component can be customized using className props and by providing custom content:

```tsx
<SecondaryHero
  className="custom-hero-class"
  heading={<CustomHeading />}
  subheading={<CustomSubheading />}
  pill={<CustomPill />}
>
  <CustomContent />
</SecondaryHero>
```

## Integration

The Secondary Hero works well with other components:

```tsx
<SecondaryHero heading="Newsletter" subheading="Stay updated">
  <NewsletterSignupContainer />
</SecondaryHero>
```
