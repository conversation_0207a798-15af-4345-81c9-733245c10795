import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import type { Meta, StoryFn } from '@storybook/nextjs';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import {
  MultiStepForm,
  MultiStepFormFooter,
  MultiStepFormHeader,
  MultiStepFormStep,
  createStepSchema,
  useMultiStepFormContext,
} from '@kit/ui/multi-step-form';

// Create a new QueryClient instance
const queryClient = new QueryClient();

const meta = {
  title: 'Organisms/MultiStepForm',
  component: MultiStepForm,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <Story />
      </QueryClientProvider>
    ),
  ],
} satisfies Meta<typeof MultiStepForm>;

export default meta;

// Example schema for a registration form
const RegistrationSchema = createStepSchema({
  personalInfo: z.object({
    firstName: z.string().min(2, 'First name is required'),
    lastName: z.string().min(2, 'Last name is required'),
  }),
  contactInfo: z.object({
    email: z.string().email('Invalid email address'),
    phone: z.string().min(10, 'Phone number is required'),
  }),
  accountInfo: z.object({
    username: z.string().min(4, 'Username must be at least 4 characters'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
  }),
});

// Default multi-step form
export const Default: StoryFn<typeof MultiStepForm> = () => {
  const form = useForm({
    resolver: zodResolver(RegistrationSchema),
    defaultValues: {
      personalInfo: { firstName: '', lastName: '' },
      contactInfo: { email: '', phone: '' },
      accountInfo: { username: '', password: '' },
    },
  });

  return (
    <div className="w-[600px] rounded-lg border p-6">
      <Form {...form}>
        <MultiStepForm
          schema={RegistrationSchema}
          form={form}
          onSubmit={(data) => console.log('Form submitted:', data)}
        >
          <MultiStepFormHeader className="mb-8">
            <h2 className="text-2xl font-bold">Create Account</h2>
          </MultiStepFormHeader>

          <MultiStepFormStep name="personalInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="personalInfo.firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="personalInfo.lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <MultiStepFormStep name="contactInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="contactInfo.email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contactInfo.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="+****************"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <MultiStepFormStep name="accountInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="accountInfo.username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="johndoe" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="accountInfo.password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <MultiStepFormFooter className="mt-8 flex justify-between">
            <Button type="button" variant="outline">
              Previous
            </Button>
            <Button type="submit">Next</Button>
          </MultiStepFormFooter>
        </MultiStepForm>
      </Form>
    </div>
  );
};

// Multi-step form with progress indicator
export const WithProgress: StoryFn<typeof MultiStepForm> = () => {
  const form = useForm({
    resolver: zodResolver(RegistrationSchema),
    defaultValues: {
      personalInfo: { firstName: '', lastName: '' },
      contactInfo: { email: '', phone: '' },
      accountInfo: { username: '', password: '' },
    },
  });

  function ProgressHeader() {
    const { currentStepIndex, totalSteps } = useMultiStepFormContext();
    return (
      <MultiStepFormHeader className="mb-8 space-y-4">
        <h2 className="text-2xl font-bold">Create Account</h2>
        <div className="flex justify-between">
          <span className="text-muted-foreground text-sm">
            Step {currentStepIndex + 1} of {totalSteps}
          </span>
          <div className="bg-muted h-2 w-full max-w-xs rounded-full">
            <div
              className="bg-primary h-full rounded-full transition-all"
              style={{
                width: `${((currentStepIndex + 1) / totalSteps) * 100}%`,
              }}
            />
          </div>
        </div>
      </MultiStepFormHeader>
    );
  }

  function FormFooter() {
    const { isLastStep } = useMultiStepFormContext();
    return (
      <MultiStepFormFooter className="mt-8 flex justify-between">
        <Button type="button" variant="outline">
          Previous
        </Button>
        <Button type="submit">{isLastStep ? 'Submit' : 'Next'}</Button>
      </MultiStepFormFooter>
    );
  }

  return (
    <div className="w-[600px] rounded-lg border p-6">
      <Form {...form}>
        <MultiStepForm
          schema={RegistrationSchema}
          form={form}
          onSubmit={(data) => console.log('Form submitted:', data)}
        >
          <ProgressHeader />

          <MultiStepFormStep name="personalInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="personalInfo.firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="personalInfo.lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <MultiStepFormStep name="contactInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="contactInfo.email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="contactInfo.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="+****************"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <MultiStepFormStep name="accountInfo">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="accountInfo.username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="johndoe" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="accountInfo.password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </MultiStepFormStep>

          <FormFooter />
        </MultiStepForm>
      </Form>
    </div>
  );
};
