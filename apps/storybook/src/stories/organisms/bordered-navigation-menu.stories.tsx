'use client';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Home, Settings, Users } from 'lucide-react';

import {
  BorderedNavigationMenu,
  BorderedNavigationMenuItem,
} from '@kit/ui/bordered-navigation-menu';

const meta = {
  title: 'Organisms/BorderedNavigationMenu',
  component: BorderedNavigationMenu,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof BorderedNavigationMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <BorderedNavigationMenu>
      <BorderedNavigationMenuItem path="/" label={<span>Home</span>} />
      <BorderedNavigationMenuItem
        path="/dashboard"
        label={<span>Dashboard</span>}
      />
      <BorderedNavigationMenuItem
        path="/settings"
        label={<span>Settings</span>}
      />
    </BorderedNavigationMenu>
  ),
};

export const WithIcons: Story = {
  render: () => (
    <BorderedNavigationMenu>
      <BorderedNavigationMenuItem
        path="/"
        label={
          <div className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            <span>Home</span>
          </div>
        }
      />
      <BorderedNavigationMenuItem
        path="/users"
        label={
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Users</span>
          </div>
        }
      />
      <BorderedNavigationMenuItem
        path="/settings"
        label={
          <div className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </div>
        }
      />
    </BorderedNavigationMenu>
  ),
};

export const ActiveState: Story = {
  render: () => (
    <BorderedNavigationMenu>
      <BorderedNavigationMenuItem path="/" label={<span>Home</span>} active />
      <BorderedNavigationMenuItem
        path="/dashboard"
        label={<span>Dashboard</span>}
      />
      <BorderedNavigationMenuItem
        path="/settings"
        label={<span>Settings</span>}
      />
    </BorderedNavigationMenu>
  ),
};

export const CustomStyles: Story = {
  render: () => (
    <BorderedNavigationMenu>
      <BorderedNavigationMenuItem
        path="/"
        label={<span>Home</span>}
        className="px-4"
        buttonClassName="font-bold"
      />
      <BorderedNavigationMenuItem
        path="/dashboard"
        label={<span>Dashboard</span>}
        className="px-4"
        buttonClassName="text-primary"
      />
      <BorderedNavigationMenuItem
        path="/settings"
        label={<span>Settings</span>}
        className="px-4"
        buttonClassName="italic"
      />
    </BorderedNavigationMenu>
  ),
};
