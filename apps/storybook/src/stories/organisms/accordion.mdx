import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as AccordionStories from './accordion.stories';

<Meta of={AccordionStories} />

# Accordion

An expandable/collapsible content area built using Radix UI Accordion primitive. Perfect for creating FAQ sections, nested navigation menus, or any content that needs to be organized in collapsible sections.

## Features

- Single or multiple expanded sections
- Smooth animations
- Keyboard navigation
- Screen reader friendly
- Customizable triggers
- Nested accordions support
- Dark theme compatible
- WAI-ARIA compliant

## Usage

```tsx
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@kit/ui/accordion';

function MyAccordion() {
  return (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger>Section Title</AccordionTrigger>
        <AccordionContent>Section content goes here.</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
```

## Examples

### Default

Basic accordion with single item expansion and collapsible behavior.

<Canvas of={AccordionStories.Default} />

### Multiple

Accordion that allows multiple sections to be expanded simultaneously.

<Canvas of={AccordionStories.Multiple} />

### With Custom Trigger

Customized trigger styles with icons and colors.

<Canvas of={AccordionStories.WithCustomTrigger} />

### Nested

Accordion with nested sections for complex hierarchical content.

<Canvas of={AccordionStories.Nested} />

### With Long Content

Example showing how the accordion handles longer content with smooth animations.

<Canvas of={AccordionStories.WithLongContent} />

## Component API

<Controls />

## Subcomponents

### Accordion

The root component that wraps the accordion items.

Props:

- `type`: "single" | "multiple" - Controls expansion behavior
- `collapsible`: boolean - Whether items can be collapsed (single type only)
- `value`/`defaultValue`: string | string[] - Controlled/uncontrolled values
- `onValueChange`: (value: string | string[]) => void - Value change handler

### AccordionItem

Container for individual accordion sections.

Props:

- `value`: string - Unique identifier for the item
- `disabled`: boolean - Whether the item is disabled

### AccordionTrigger

The button that expands/collapses its section.

Props:

- `className`: string - Additional CSS classes
- All HTML button attributes

### AccordionContent

The expandable content area.

Props:

- `className`: string - Additional CSS classes
- `forceMount`: boolean - Forces mounting when true

## Accessibility

The Accordion component follows WAI-ARIA guidelines:

### Keyboard Navigation

- `Space/Enter`: Toggle the focused section
- `Tab`: Move focus between focusable elements
- `Shift + Tab`: Move focus to previous focusable element
- `Home`: Move focus to first trigger
- `End`: Move focus to last trigger

### ARIA Attributes

- Uses `role="region"` for content
- Manages `aria-expanded` state
- Provides `aria-controls`
- Sets `aria-labelledby`
- Handles focus management

## Guidelines

### Usage Guidelines

1. Content Organization

   - Group related content
   - Keep content concise
   - Use clear headings
   - Consider nesting depth

2. Visual Design

   - Maintain consistent spacing
   - Use clear indicators
   - Consider motion
   - Support dark theme

3. Interaction Design
   - Provide clear feedback
   - Handle loading states
   - Consider animations
   - Support touch devices

### Best Practices

1. Implementation

   - Use semantic markup
   - Handle dynamic content
   - Manage state properly
   - Consider SSR

2. Performance

   - Optimize animations
   - Lazy load content
   - Handle large datasets
   - Monitor memory usage

3. Accessibility
   - Test keyboard navigation
   - Verify screen readers
   - Check color contrast
   - Support reduced motion
