import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as BorderedNavigationMenuStories from './bordered-navigation-menu.stories';

<Meta of={BorderedNavigationMenuStories} />

# Bordered Navigation Menu

A navigation menu component with a clean, bordered design featuring active state indicators and support for icons. Built on top of the base NavigationMenu component, it provides a consistent and accessible navigation experience.

## Features

- Clean, bordered design with active state indicators
- Support for icons and custom content
- Automatic active state detection based on current route
- Customizable styles through className props
- Built-in i18n support through Trans component
- Keyboard navigation support
- Mobile-responsive design
- Dark mode compatible
- Accessible by default

## Installation

```tsx
import {
  BorderedNavigationMenu,
  BorderedNavigationMenuItem,
} from '@kit/ui/bordered-navigation-menu';
```

## Examples

### Default

Basic navigation menu with text labels.

<Canvas of={BorderedNavigationMenuStories.Default} />

### With Icons

Navigation menu items with icons for better visual hierarchy.

<Canvas of={BorderedNavigationMenuStories.WithIcons} />

### Active State

Demonstrating the active state indicator.

<Canvas of={BorderedNavigationMenuStories.ActiveState} />

### Custom Styles

Customizing the appearance using className props.

<Canvas of={BorderedNavigationMenuStories.CustomStyles} />

## Component API

### BorderedNavigationMenu

The root component that wraps the navigation items.

<Controls />

### BorderedNavigationMenuItem

Individual navigation item component.

Props:

- `path` (string, required): The navigation path for the item
- `label` (React.ReactNode | string, required): The content to display
- `end` (boolean | ((path: string) => boolean), optional): Route matching strategy
- `active` (boolean, optional): Force active state
- `className` (string, optional): Additional classes for the item container
- `buttonClassName` (string, optional): Additional classes for the button element

## Accessibility

### Keyboard Navigation

- `Tab`: Move focus between menu items
- `Enter/Space`: Activate the focused item
- `Arrow Keys`: Navigate between menu items
- `Home/End`: Jump to first/last item

### Screen Readers

- Uses semantic HTML elements
- Implements proper ARIA roles and attributes
- Provides descriptive labels
- Announces active states
- Manages focus appropriately

## Guidelines

### Usage Guidelines

1. Navigation Structure

   - Keep the menu structure simple and flat
   - Use clear, concise labels
   - Group related items together
   - Consider mobile breakpoints
   - Maintain consistent spacing

2. Visual Hierarchy

   - Use icons consistently
   - Highlight active states clearly
   - Maintain adequate contrast
   - Consider hover states
   - Use appropriate spacing

3. Content
   - Keep labels short and descriptive
   - Use meaningful icons
   - Provide clear active indicators
   - Consider i18n requirements
   - Handle long text gracefully

### Best Practices

1. Implementation

   - Initialize with proper paths
   - Handle route changes
   - Manage active states
   - Consider loading states
   - Handle errors gracefully

2. Performance

   - Minimize re-renders
   - Optimize icon usage
   - Handle route changes efficiently
   - Consider code splitting
   - Monitor bundle size

3. Accessibility
   - Test keyboard navigation
   - Verify screen reader compatibility
   - Ensure sufficient contrast
   - Provide text alternatives
   - Maintain focus management

## CSS Customization

### Global Styles

```css
.bordered-navigation-menu {
  /* Base styles */
  --navigation-height: 3rem;
  --navigation-background: hsl(var(--background));
  --navigation-border: hsl(var(--border));
  --navigation-text: hsl(var(--foreground));

  /* Active state */
  --navigation-active-border: hsl(var(--primary));
  --navigation-active-text: hsl(var(--primary-foreground));
}
```

### Styling Guidelines

1. Layout

   - Use consistent spacing
   - Consider mobile breakpoints
   - Handle overflow gracefully
   - Maintain alignment
   - Support responsive design

2. Colors

   - Follow brand guidelines
   - Ensure contrast ratios
   - Support dark mode
   - Use semantic colors
   - Consider state changes

3. Typography
   - Use consistent font sizes
   - Maintain readability
   - Handle text overflow
   - Support different lengths
   - Consider line height

## API Reference

### Types

```tsx
interface BorderedNavigationMenuProps {
  children: React.ReactNode;
  className?: string;
}

interface BorderedNavigationMenuItemProps {
  path: string;
  label: React.ReactNode | string;
  end?: boolean | ((path: string) => boolean);
  active?: boolean;
  className?: string;
  buttonClassName?: string;
}
```

### State Management

The component manages several internal states:

- Route matching for active state
- Hover and focus states
- Mobile responsiveness
- Theme compatibility
- Internationalization
