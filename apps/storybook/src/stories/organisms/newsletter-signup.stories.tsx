import type { Meta, StoryObj } from '@storybook/nextjs';

import { NewsletterSignup } from '@kit/ui/dojo/molecules/newsletter-signup';

const meta = {
  title: 'Organisms/Newsletter Signup',
  component: NewsletterSignup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof NewsletterSignup>;

export default meta;
type Story = StoryObj<typeof NewsletterSignup>;

export const Default: Story = {
  args: {
    onSignup: async (params: { email: string }) => {
      console.log('Newsletter signup:', params);
    },
  },
};

export const WithCustomText: Story = {
  args: {
    onSignup: async (params: { email: string }) => {
      console.log('Newsletter signup:', params);
    },
    buttonText: 'Join Now',
    placeholder: 'Your email address',
  },
};

export const WithCustomStyling: Story = {
  args: {
    onSignup: async (params: { email: string }) => {
      console.log('Newsletter signup:', params);
    },
    className: 'max-w-md',
    buttonText: 'Get Updates',
    placeholder: 'Enter your work email',
  },
};
