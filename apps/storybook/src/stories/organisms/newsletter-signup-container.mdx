import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as NewsletterSignupContainerStories from './newsletter-signup-container.stories';

<Meta of={NewsletterSignupContainerStories} />

# Newsletter Signup Container

The Newsletter Signup Container is a higher-level component that wraps the Newsletter Signup form with additional features like loading states, success/error messages, and customizable content.

## Features

- Loading state management
- Success and error message display
- Customizable headings and descriptions
- Responsive design
- Dark mode compatible
- Error handling

## Usage

```tsx
import { NewsletterSignupContainer } from '@kit/ui/newsletter-signup-container';

export default function Page() {
  const handleSignup = async (email: string) => {
    // Handle signup logic
    await subscribeToNewsletter(email);
  };

  return (
    <NewsletterSignupContainer
      onSignup={handleSignup}
      heading="Subscribe to our newsletter"
      description="Get the latest updates directly to your inbox."
    />
  );
}
```

## Examples

### Default

A basic newsletter signup container with default content.

<Canvas of={NewsletterSignupContainerStories.Default} />

### With Custom Messages

Customized headings and messages for different states.

<Canvas of={NewsletterSignupContainerStories.WithCustomMessages} />

### With Custom Styling

Custom styling applied to the container.

<Canvas of={NewsletterSignupContainerStories.WithCustomStyling} />

## Component API

<Controls />

## States

The component manages four different states:

1. **Idle**: Shows the signup form
2. **Loading**: Displays a loading spinner
3. **Success**: Shows a success message
4. **Error**: Shows an error message

## Accessibility

The Newsletter Signup Container follows accessibility best practices:

- Proper heading hierarchy
- Status announcements
- Loading state indicators
- ARIA live regions for status updates
- High contrast text colors

## Best Practices

1. Keep headings clear and concise
2. Provide informative success messages
3. Write helpful error messages
4. Consider loading state duration
5. Test all possible states
6. Ensure responsive behavior

## Error Handling

The component handles errors gracefully:

```typescript
try {
  await onSignup(email);
  // Show success message
} catch (error) {
  // Show error message
}
```

## Customization

The component can be customized using className props and by providing custom content:

```tsx
<NewsletterSignupContainer
  className="custom-container-class"
  heading="Custom Heading"
  description="Custom description text"
  successMessage="Custom success message"
  errorMessage="Custom error message"
  onSignup={handleSignup}
/>
```

## Integration

The container component works seamlessly with the Newsletter Signup component:

```tsx
<NewsletterSignupContainer>
  {status === 'idle' && <NewsletterSignup onSignup={handleSubmit} />}
</NewsletterSignupContainer>
```
