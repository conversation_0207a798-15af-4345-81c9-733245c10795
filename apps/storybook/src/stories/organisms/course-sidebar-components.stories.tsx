import { useState } from 'react';

import type { Meta, StoryFn } from '@storybook/nextjs';
import { <PERSON><PERSON>ef<PERSON>, ArrowRight } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { SidebarProvider } from '@kit/ui/shadcn-sidebar';

import {
  CourseSidebarShadcn,
  SidebarChapterItemShadcn,
  SidebarLessonItemShadcn,
} from './mocked-components';

// Mock SidebarToggle component since we don't have access to the real one in storybook
const SidebarToggle = ({
  children,
}: {
  children?: React.ReactNode | ((open: boolean) => React.ReactNode);
}) => {
  const [open, setOpen] = useState(true);

  const handleToggle = () => {
    setOpen(!open);
  };

  return (
    <div onClick={handleToggle}>
      {children ? (
        typeof children === 'function' ? (
          children(open)
        ) : (
          children
        )
      ) : (
        <Button variant="outline" size="sm">
          {open ? 'Hide' : 'Show'} Sidebar
        </Button>
      )}
    </div>
  );
};

// Mock data - similar to what we use in the actual application
const mockCourse = {
  id: 'course-1',
  slug: 'react-masterclass',
  title: 'React Masterclass',
};

const mockChapters = [
  {
    id: 'chapter-1',
    title: 'Getting Started with React',
    sequence_order: 1,
    course_id: 'course-1',
  },
  {
    id: 'chapter-2',
    title: 'Advanced React Patterns',
    sequence_order: 2,
    course_id: 'course-1',
  },
];

const mockLessons = [
  {
    id: 'lesson-1',
    title: 'Introduction to React',
    chapter_id: 'chapter-1',
    sequence_order: 1,
    status: 'published',
    content_type: 'video',
    _count: { quiz_questions: 0 },
  },
  {
    id: 'lesson-2',
    title: 'Setting Up Your Environment',
    chapter_id: 'chapter-1',
    sequence_order: 2,
    status: 'published',
    content_type: 'video',
    _count: { quiz_questions: 0 },
  },
  {
    id: 'lesson-3',
    title: 'Components and Props',
    chapter_id: 'chapter-2',
    sequence_order: 1,
    status: 'published',
    content_type: 'video',
    _count: { quiz_questions: 0 },
  },
  {
    id: 'lesson-4',
    title: 'State and Lifecycle',
    chapter_id: 'chapter-2',
    sequence_order: 2,
    status: 'draft',
    content_type: 'text',
    _count: { quiz_questions: 0 },
  },
  {
    id: 'lesson-5',
    title: 'Standalone Lesson',
    chapter_id: null,
    sequence_order: 1,
    status: 'published',
    content_type: 'exercise',
    _count: { quiz_questions: 5 },
  },
];

const meta = {
  title: 'Course/Organisms/CourseSidebar/Components',
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="border-border h-[500px] w-[300px] overflow-hidden border">
        <Story />
      </div>
    ),
  ],
} satisfies Meta;

export default meta;

// Individual Chapter Item Story
export const ChapterItem: StoryFn = () => {
  const [expanded, setExpanded] = useState(true);

  return (
    <div className="p-2">
      <SidebarChapterItemShadcn
        chapter={mockChapters[0]!}
        lessons={mockLessons.filter((l) => l.chapter_id === 'chapter-1')}
        expanded={expanded}
        onToggleExpanded={() => setExpanded(!expanded)}
        canMoveChapter
        isEditable
        currentLessonId="lesson-1"
        onLessonClick={() => {}}
      />
    </div>
  );
};

// Individual Lesson Item Story
export const LessonItem: StoryFn = () => {
  return (
    <div className="p-4">
      <h3 className="mb-2 text-sm font-medium">Published Video Lesson</h3>
      <SidebarLessonItemShadcn
        lesson={mockLessons[0]!}
        isActive={false}
        canEdit={true}
        onClick={() => {}}
      />

      <h3 className="mb-2 mt-4 text-sm font-medium">Active Lesson</h3>
      <SidebarLessonItemShadcn
        lesson={mockLessons[0]!}
        isActive={true}
        canEdit={true}
        onClick={() => {}}
      />

      <h3 className="mb-2 mt-4 text-sm font-medium">Draft Text Lesson</h3>
      <SidebarLessonItemShadcn
        lesson={mockLessons[3]!}
        isActive={false}
        canEdit={true}
        onClick={() => {}}
      />

      <h3 className="mb-2 mt-4 text-sm font-medium">
        Exercise Lesson with Quiz
      </h3>
      <SidebarLessonItemShadcn
        lesson={mockLessons[4]!}
        isActive={false}
        canEdit={true}
        onClick={() => {}}
      />
    </div>
  );
};

// Sidebar Toggle Story
export const SidebarToggleComponent: StoryFn = () => {
  return (
    <SidebarProvider>
      <div className="flex h-full items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="border-muted rounded-md border p-4">
            <SidebarToggle />
          </div>

          <div className="border-muted rounded-md border p-4">
            <SidebarToggle>
              <Button variant="outline" size="sm">
                Toggle <ArrowLeft className="ml-2 h-4 w-4" />
              </Button>
            </SidebarToggle>
          </div>

          <div className="border-muted rounded-md border p-4">
            <SidebarToggle>
              {(open) => (
                <Button variant="outline" size="icon">
                  {open ? (
                    <ArrowLeft className="h-4 w-4" />
                  ) : (
                    <ArrowRight className="h-4 w-4" />
                  )}
                </Button>
              )}
            </SidebarToggle>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
};

// Complete Course Sidebar Story
export const CompleteSidebar: StoryFn = () => {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <CourseSidebarShadcn
          course={mockCourse}
          chapters={mockChapters}
          lessons={mockLessons}
          currentLessonId="lesson-1"
          onLessonClick={() => {}}
          canEdit={true}
        />
      </div>
    </SidebarProvider>
  );
};

// Complete Course Sidebar - View Only
export const CompleteSidebarViewOnly: StoryFn = () => {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <CourseSidebarShadcn
          course={mockCourse}
          chapters={mockChapters}
          lessons={mockLessons}
          currentLessonId="lesson-1"
          onLessonClick={() => {}}
          canEdit={false}
        />
      </div>
    </SidebarProvider>
  );
};

// Complete Course Sidebar - Collapsed
export const CompleteSidebarCollapsed: StoryFn = () => {
  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-full">
        <CourseSidebarShadcn
          course={mockCourse}
          chapters={mockChapters}
          lessons={mockLessons}
          currentLessonId="lesson-1"
          onLessonClick={() => {}}
          canEdit={true}
        />
      </div>
    </SidebarProvider>
  );
};
