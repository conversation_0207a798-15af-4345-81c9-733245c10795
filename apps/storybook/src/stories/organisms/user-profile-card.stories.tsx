import type { Meta, StoryObj } from '@storybook/nextjs';

import { UserProfileCard } from '@kit/ui/dojo/organisms/user-profile-card';

const meta = {
  title: 'Organisms/UserProfileCard',
  component: UserProfileCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof UserProfileCard>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default UserProfileCard with minimal information
 */
export const Default: Story = {
  args: {
    publicProfile: {
      id: 'user-123',
      firstName: '<PERSON>',
      lastName: 'Doe',
      username: 'johndoe',
      pictureUrl: '',
      createdAt: '2023-01-01T00:00:00Z',
      lastSignInAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
      country: '',
      countryIcon: '',
      userDetails: {},
    },
  },
};

/**
 * UserProfileCard with profile picture and bio
 */
export const WithPictureAndBio: Story = {
  args: {
    publicProfile: {
      id: 'user-456',
      firstName: 'Jane',
      lastName: 'Doe',
      username: 'janedoe',
      pictureUrl: 'https://i.pravatar.cc/300',
      createdAt: '2023-02-15T00:00:00Z',
      lastSignInAt: '2023-02-15T00:00:00Z',
      updatedAt: '2023-02-15T00:00:00Z',
      country: '',
      countryIcon: '',
      userDetails: {
        bio: 'Front-end developer with a passion for UI/UX design. I love building beautiful and user-friendly interfaces.',
        role: 'Senior Developer',
      },
    },
  },
};

/**
 * UserProfileCard with completed verified user profile
 */
export const CompleteProfile: Story = {
  args: {
    publicProfile: {
      id: 'user-789',
      firstName: 'Alex',
      lastName: 'Smith',
      username: 'techwriter',
      pictureUrl: 'https://i.pravatar.cc/300?img=68',
      createdAt: '2023-01-15T08:30:00Z',
      lastSignInAt: '2024-03-20T14:20:00Z',
      updatedAt: '2024-03-10T09:15:00Z',
      country: 'United States',
      countryIcon: '🇺🇸',
      userDetails: {
        bio: 'Technical writer and education specialist with 10+ years experience. I create clear, concise documentation that helps users succeed.',
        company: 'TechDocs Inc.',
        role: 'Lead Technical Writer',
      },
    },
  },
};

/**
 * User with social media links
 */
export const WithSocialLinks: Story = {
  args: {
    publicProfile: {
      id: 'user-101',
      firstName: 'Social',
      lastName: 'Butterfly',
      username: 'socialbutterfly',
      pictureUrl: 'https://i.pravatar.cc/300?img=32',
      createdAt: '2023-03-10T00:00:00Z',
      lastSignInAt: '2023-03-10T00:00:00Z',
      updatedAt: '2023-03-10T00:00:00Z',
      country: '',
      countryIcon: '',
      userDetails: {
        bio: 'Connecting people through technology. I believe in the power of social media for positive change.',
        socialLinks: {
          twitter: 'https://twitter.com/socialbutterfly',
          linkedin: 'https://linkedin.com/in/socialbutterfly',
          github: 'https://github.com/socialbutterfly',
        },
      },
    },
  },
};

/**
 * Profile with location information
 */
export const WithLocation: Story = {
  args: {
    publicProfile: {
      id: 'user-202',
      firstName: 'Design',
      lastName: 'Guru',
      username: 'designguru',
      pictureUrl: 'https://i.pravatar.cc/300?img=12',
      createdAt: '2023-04-20T00:00:00Z',
      lastSignInAt: '2023-04-20T00:00:00Z',
      updatedAt: '2023-04-20T00:00:00Z',
      country: 'Canada',
      countryIcon: '🇨🇦',
      userDetails: {},
    },
  },
};

/**
 * Profile with very long bio text
 */
export const LongBio: Story = {
  args: {
    publicProfile: {
      id: 'user-303',
      firstName: 'Wordy',
      lastName: 'Author',
      username: 'longwriter',
      pictureUrl: 'https://i.pravatar.cc/300?img=50',
      createdAt: '2023-05-15T00:00:00Z',
      lastSignInAt: '2023-05-15T00:00:00Z',
      updatedAt: '2023-05-15T00:00:00Z',
      country: '',
      countryIcon: '',
      userDetails: {
        bio: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
      },
    },
  },
};

/**
 * Profile with account activity info
 */
export const AccountActivity: Story = {
  args: {
    publicProfile: {
      id: 'user-404',
      firstName: 'Anonymous',
      lastName: 'User',
      username: 'anonymous',
      pictureUrl: '',
      createdAt: '2022-07-18T08:30:00Z',
      lastSignInAt: '2023-09-30T14:20:00Z',
      updatedAt: '2023-08-15T11:45:00Z',
      country: '',
      countryIcon: '',
      userDetails: {},
    },
  },
};

/**
 * Edge case: missing username field
 */
export const MissingSlug: Story = {
  args: {
    publicProfile: {
      id: 'user-505',
      firstName: 'Missing',
      lastName: 'Slug',
      username: '', // Empty username
      pictureUrl: 'https://i.pravatar.cc/300?img=22',
      createdAt: '2023-06-01T00:00:00Z',
      lastSignInAt: '2023-06-01T00:00:00Z',
      updatedAt: '2023-06-01T00:00:00Z',
      country: 'Spain',
      countryIcon: '🇪🇸',
      userDetails: {},
    },
  },
};
