import Image from 'next/image';

import type { Meta, StoryObj } from '@storybook/nextjs';
import { Laptop, Shield, Zap } from 'lucide-react';

import {
  FeatureShowcase,
  FeatureShowcaseIconContainer,
} from '@kit/ui/dojo/organisms/feature-showcase';

const meta = {
  title: 'Organisms/Feature Showcase',
  component: FeatureShowcase,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof FeatureShowcase>;

export default meta;
type Story = StoryObj<typeof FeatureShowcase>;

const defaultFeatures = [
  {
    title: 'Lightning Fast Performance',
    description: 'Experience blazing fast load times and smooth interactions.',
    icon: <Zap className="text-primary h-5 w-5" />,
  },
  {
    title: 'Enterprise Security',
    description:
      'Bank-grade encryption and security measures to protect your data.',
    icon: <Shield className="text-primary h-5 w-5" />,
  },
  {
    title: 'Modern Technology Stack',
    description:
      'Built with the latest web technologies for the best experience.',
    icon: <Laptop className="text-primary h-5 w-5" />,
  },
];

const FeatureList = ({ features }: { features: typeof defaultFeatures }) => (
  <div className="grid gap-6">
    {features.map((feature, index) => (
      <div key={index} className="relative flex flex-col gap-2">
        <dt className="text-foreground flex items-center gap-3 text-base font-semibold">
          {feature.icon && (
            <FeatureShowcaseIconContainer>
              {feature.icon}
            </FeatureShowcaseIconContainer>
          )}
          {feature.title}
        </dt>
        <dd className="text-muted-foreground text-sm">{feature.description}</dd>
      </div>
    ))}
  </div>
);

export const Default: Story = {
  args: {
    heading: 'Built for the Modern Web',
    icon: <Zap className="text-primary h-6 w-6" />,
    children: <FeatureList features={defaultFeatures} />,
  },
};

export const WithoutIcon: Story = {
  args: {
    heading: 'Built for the Modern Web',
    children: <FeatureList features={defaultFeatures} />,
  },
};

export const WithCustomContent: Story = {
  args: {
    heading: 'Our Platform',
    icon: (
      <div className="relative h-12 w-12 overflow-hidden rounded-xl">
        <Image
          src="https://i.pravatar.cc/200"
          alt="Logo"
          width={48}
          height={48}
          className="h-full w-full object-cover"
        />
      </div>
    ),
    children: (
      <div className="grid gap-8">
        <FeatureList features={defaultFeatures} />
        <div className="flex justify-start">
          <a
            href="#"
            className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow-xs"
          >
            Learn More
          </a>
        </div>
      </div>
    ),
  },
};
