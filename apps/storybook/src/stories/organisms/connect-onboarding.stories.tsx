import type { Meta, StoryObj } from '@storybook/nextjs';

import { ConnectOnboarding } from '@kit/ui/dojo/organisms/connect-onboarding';

const meta: Meta<typeof ConnectOnboarding> = {
  title: 'Organisms/ConnectOnboarding',
  component: ConnectOnboarding,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ConnectOnboarding>;

export const InProgress: Story = {
  args: {
    status: {
      detailsSubmitted: true,
      chargesEnabled: true,
      payoutsEnabled: false,
    },
    requirements: {
      currentlyDue: ['external_account'],
      eventuallyDue: ['business_profile_mcc'],
    },
    onContinueSetup: async () => {
      console.log('Continue setup clicked');
    },
    stripeDashboardLink: 'https://dashboard.stripe.com',
  },
};

export const Complete: Story = {
  args: {
    status: {
      detailsSubmitted: true,
      chargesEnabled: true,
      payoutsEnabled: true,
    },
    onContinueSetup: async () => {
      console.log('Continue setup clicked');
    },
    stripeDashboardLink: 'https://dashboard.stripe.com',
  },
};

export const JustStarted: Story = {
  args: {
    status: {
      detailsSubmitted: false,
      chargesEnabled: false,
      payoutsEnabled: false,
    },
    requirements: {
      currentlyDue: [
        'external_account',
        'business_profile_website',
        'business_profile_description',
      ],
      currentDeadline: Date.now() / 1000 + 86400 * 30, // 30 days from now
    },
    onContinueSetup: async () => {
      console.log('Continue setup clicked');
    },
    stripeDashboardLink: 'https://dashboard.stripe.com',
  },
};
