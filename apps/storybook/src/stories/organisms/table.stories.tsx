import type { Meta, StoryFn } from '@storybook/nextjs';

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';

const meta: Meta<typeof Table> = {
  title: 'Organisms/Table',
  component: Table,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

// Basic table with header and body
export const Default: StoryFn<typeof Table> = () => (
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>Name</TableHead>
        <TableHead>Email</TableHead>
        <TableHead>Role</TableHead>
        <TableHead className="text-right">Actions</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow>
        <TableCell>John Doe</TableCell>
        <TableCell><EMAIL></TableCell>
        <TableCell>Admin</TableCell>
        <TableCell className="text-right">Edit</TableCell>
      </TableRow>
      <TableRow>
        <TableCell><PERSON></TableCell>
        <TableCell><EMAIL></TableCell>
        <TableCell>User</TableCell>
        <TableCell className="text-right">Edit</TableCell>
      </TableRow>
    </TableBody>
  </Table>
);

// Table with caption
export const WithCaption: StoryFn<typeof Table> = () => (
  <Table>
    <TableCaption>A list of recent users.</TableCaption>
    <TableHeader>
      <TableRow>
        <TableHead>Name</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Role</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow>
        <TableCell>Alice Cooper</TableCell>
        <TableCell>Active</TableCell>
        <TableCell>Editor</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>Bob Wilson</TableCell>
        <TableCell>Inactive</TableCell>
        <TableCell>Viewer</TableCell>
      </TableRow>
    </TableBody>
  </Table>
);

// Table with footer
export const WithFooter: StoryFn<typeof Table> = () => (
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>Product</TableHead>
        <TableHead>Quantity</TableHead>
        <TableHead className="text-right">Price</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow>
        <TableCell>Product A</TableCell>
        <TableCell>2</TableCell>
        <TableCell className="text-right">$20.00</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>Product B</TableCell>
        <TableCell>1</TableCell>
        <TableCell className="text-right">$15.00</TableCell>
      </TableRow>
    </TableBody>
    <TableFooter>
      <TableRow>
        <TableCell colSpan={2}>Total</TableCell>
        <TableCell className="text-right">$35.00</TableCell>
      </TableRow>
    </TableFooter>
  </Table>
);

// Empty table with message
export const Empty: StoryFn<typeof Table> = () => (
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>Name</TableHead>
        <TableHead>Email</TableHead>
        <TableHead>Role</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow>
        <TableCell colSpan={3} className="h-24 text-center">
          No data available
        </TableCell>
      </TableRow>
    </TableBody>
  </Table>
);

// Table with hover states
export const WithHover: StoryFn<typeof Table> = () => (
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>ID</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Progress</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow className="cursor-pointer">
        <TableCell>#001</TableCell>
        <TableCell>In Progress</TableCell>
        <TableCell>75%</TableCell>
      </TableRow>
      <TableRow className="cursor-pointer">
        <TableCell>#002</TableCell>
        <TableCell>Completed</TableCell>
        <TableCell>100%</TableCell>
      </TableRow>
      <TableRow className="cursor-pointer">
        <TableCell>#003</TableCell>
        <TableCell>Pending</TableCell>
        <TableCell>0%</TableCell>
      </TableRow>
    </TableBody>
  </Table>
);
