import { <PERSON><PERSON>, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as ShadcnSidebarStories from './shadcn-sidebar.stories';

<Meta of={ShadcnSidebarStories} />

# Shadcn Sidebar

A versatile and customizable sidebar component that provides navigation and content organization for your application.

## Features

- Multiple variants: default, floating, and inset
- Collapsible functionality with keyboard shortcuts
- Responsive design with mobile support
- Minimized state with expand-on-hover capability
- Customizable width and appearance
- Accessible keyboard navigation
- Dark theme compatible

## Installation

The Shadcn Sidebar is part of the `@kit/ui` package and comes pre-installed with all its dependencies.

## Usage

```tsx
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from '@kit/ui/shadcn/sidebar';

function MyApp() {
  return (
    <SidebarProvider>
      <div className="flex h-full">
        <Sidebar>
          <SidebarHeader>
            <div className="px-2 py-4">
              <h2 className="text-lg font-semibold">My App</h2>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton>
                    <HomeIcon />
                    <span>Home</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>
        <SidebarInset>{/* Your main content */}</SidebarInset>
      </div>
    </SidebarProvider>
  );
}
```

## Examples

### Default Sidebar

A basic sidebar with navigation menu and content.

<Canvas of={ShadcnSidebarStories.Default} />

### Collapsible Sidebar

A sidebar that can be toggled using a trigger button or keyboard shortcut (Cmd/Ctrl + B).

<Canvas of={ShadcnSidebarStories.Collapsible} />

### Floating Sidebar

A floating variant with shadow and border.

<Canvas of={ShadcnSidebarStories.Floating} />

### Minimized with Expand on Hover

A minimized sidebar that expands when hovered over.

<Canvas of={ShadcnSidebarStories.MinimizedExpandOnHover} />

## Component API

### SidebarProvider

The root component that provides context for the sidebar state.

#### Props

- `defaultOpen` (boolean) - Initial open state of the sidebar. Default: `true`
- `open` (boolean) - Controlled open state
- `onOpenChange` (function) - Callback when open state changes
- `minimized` (boolean) - Whether the sidebar should be minimized. Default: `false`
- `expandOnHover` (boolean) - Whether the minimized sidebar should expand on hover

### Sidebar

The main sidebar component.

#### Props

- `side` ('left' | 'right') - Position of the sidebar. Default: 'left'
- `variant` ('sidebar' | 'floating' | 'inset' | 'ghost') - Visual variant. Default: 'sidebar'
- `collapsible` ('offcanvas' | 'icon' | 'none') - Collapsible behavior. Default: 'offcanvas'

### SidebarHeader

Container for the sidebar header content.

### SidebarContent

Container for the main sidebar content.

### SidebarFooter

Container for the sidebar footer content.

### SidebarGroup

A group of related menu items.

#### Props

- `className` (string) - Additional CSS classes

### SidebarGroupLabel

Label for a sidebar group.

#### Props

- `asChild` (boolean) - Whether to render as a child component
- `className` (string) - Additional CSS classes

### SidebarMenu

Container for menu items.

### SidebarMenuItem

Individual menu item container.

### SidebarMenuButton

Interactive button for menu items.

#### Props

- `variant` ('default' | 'outline') - Button variant. Default: 'default'
- `size` ('default' | 'sm' | 'lg') - Button size. Default: 'default'

### SidebarInset

Container for the main content area that adapts to the sidebar state.

## Accessibility

- Keyboard navigation support (Tab, Enter, Space)
- ARIA attributes for interactive elements
- Focus management for collapsible sections
- Screen reader announcements for state changes
- Keyboard shortcut (Cmd/Ctrl + B) for toggling the sidebar

## Design Guidelines

1. **Layout**

   - Keep the sidebar width consistent with the design system
   - Use appropriate spacing between menu items
   - Maintain visual hierarchy with clear sections

2. **Content**

   - Use clear, descriptive labels for navigation items
   - Include icons to enhance visual recognition
   - Limit the number of primary navigation items

3. **Interaction**

   - Provide clear visual feedback for hover and active states
   - Ensure smooth transitions for collapsible behavior
   - Make the sidebar toggle easily accessible

4. **Responsive Design**
   - Adapt to different screen sizes
   - Transform into a mobile-friendly overlay on small screens
   - Maintain usability across all device types

## Technical Details

### Performance Considerations

- Uses CSS transitions for smooth animations
- Implements React.memo for optimized rendering
- Lazy loads icons and complex components
- Minimizes layout shifts during transitions

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Graceful degradation for older browsers
- RTL language support

### Dependencies

- React 18+
- Radix UI for primitives
- Lucide React for icons
- Class Variance Authority for variants
- Tailwind CSS for styling

## Best Practices

1. **State Management**

   - Use the SidebarProvider at the app root level
   - Maintain sidebar state persistence with cookies
   - Handle state changes efficiently

2. **Customization**

   - Use Tailwind CSS classes for styling
   - Extend variants through class-variance-authority
   - Follow the design system's color scheme

3. **Integration**

   - Integrate with your routing system
   - Handle navigation state properly
   - Manage focus when content changes

4. **Mobile Considerations**
   - Test touch interactions thoroughly
   - Ensure proper gesture support
   - Optimize for different screen sizes
