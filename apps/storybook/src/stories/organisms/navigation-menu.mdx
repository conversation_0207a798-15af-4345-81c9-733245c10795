import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as NavigationMenuStories from './navigation-menu.stories';

<Meta of={NavigationMenuStories} />

# Navigation Menu

A fully accessible navigation menu component built on top of Radix UI, featuring dropdowns, keyboard navigation, and hover interactions.

## Features

- Accessible dropdown navigation
- Keyboard navigation support
- Hover and focus states
- Support for icons
- Flexible content layouts
- Dark mode compatible
- Responsive design
- Automatic ARIA attributes
- Focus management

## Usage

```tsx
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@kit/ui/navigation-menu';

export function MyNavigation() {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuLink className={navigationMenuTriggerStyle()} href="/">
            Home
          </NavigationMenuLink>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Dropdown</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[200px] gap-3 p-4">
              <li>
                <NavigationMenuLink
                  className="hover:bg-accent block rounded-md p-2"
                  href="/item"
                >
                  Menu Item
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}
```

## Examples

### Basic Navigation

Simple horizontal navigation with direct links.

<Canvas of={NavigationMenuStories.Default} />

### With Dropdowns

Navigation menu with rich dropdown content.

<Canvas of={NavigationMenuStories.WithDropdowns} />

### With Icons

Navigation menu items with icons for better visual hierarchy.

<Canvas of={NavigationMenuStories.WithIcons} />

## Component API

<Controls />

## Subcomponents

### NavigationMenu

The root navigation component that wraps the entire menu structure.

Props:

- `defaultValue`: The default selected menu item
- `value`: Controlled selected value
- `onValueChange`: Called when selection changes
- All HTML nav attributes

### NavigationMenuList

Container for navigation items. Must be a direct child of NavigationMenu.

Props:

- `className`: Additional CSS classes
- All HTML ul attributes

### NavigationMenuItem

Individual menu item container.

Props:

- `value`: Unique identifier for the item
- `className`: Additional CSS classes
- All HTML li attributes

### NavigationMenuTrigger

Button that triggers dropdown content.

Props:

- `className`: Additional CSS classes
- All HTML button attributes

### NavigationMenuContent

Dropdown content container.

Props:

- `className`: Additional CSS classes
- `forceMount`: Force mounting when parent is not open
- All HTML div attributes

### NavigationMenuLink

Navigation link component.

Props:

- `href`: URL for navigation
- `className`: Additional CSS classes
- `asChild`: Merge props with child element
- All HTML a attributes

## Accessibility

### Keyboard Navigation

- `Tab`: Move focus between menu items
- `Space/Enter`: Activate menu item or trigger
- `Arrow Keys`: Navigate between menu items
- `Escape`: Close open dropdown
- `Home/End`: Jump to first/last item

### ARIA Attributes

- Uses `role="navigation"` for the menu
- Uses `role="menuitem"` for items
- Manages focus appropriately
- Provides proper labeling
- Announces dropdown states

## Guidelines

### Usage Guidelines

1. Menu Structure

   - Keep hierarchy simple
   - Group related items
   - Use clear labels
   - Consider mobile users
   - Limit dropdown depth

2. Content Organization

   - Prioritize important items
   - Use consistent styling
   - Add visual hierarchy
   - Keep dropdowns focused
   - Consider load time

3. Interaction Design
   - Provide visual feedback
   - Handle hover states
   - Manage focus states
   - Consider touch devices
   - Support keyboard users

### Best Practices

1. Implementation

   - Initialize properly
   - Handle loading states
   - Manage focus correctly
   - Clean up listeners
   - Consider SSR

2. Content

   - Use descriptive labels
   - Add helpful icons
   - Group logically
   - Keep it simple
   - Consider translations

3. Performance
   - Optimize animations
   - Lazy load content
   - Monitor render cycles
   - Handle edge cases
   - Test thoroughly

## CSS Customization

The component uses CSS variables for easy customization:

```css
--navigation-menu-height
--navigation-menu-viewport-width
--navigation-menu-viewport-height
```

You can also use the `navigationMenuTriggerStyle()` function for consistent trigger styling.

### Styling Guidelines

1. Visual Hierarchy

   - Use consistent spacing
   - Maintain contrast ratios
   - Consider hover states
   - Style active states
   - Handle focus rings

2. Responsive Design

   - Mobile-first approach
   - Handle breakpoints
   - Adjust spacing
   - Consider touch targets
   - Test viewport sizes

3. Theme Integration
   - Support dark mode
   - Use CSS variables
   - Follow brand colors
   - Maintain accessibility
   - Consider animations
