import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { <PERSON><PERSON>Banner } from '@kit/ui/cookie-banner';

const meta = {
  title: 'Organisms/CookieBanner',
  component: CookieBanner,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="relative h-[400px] w-full">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof CookieBanner>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default cookie banner
export const Default: Story = {
  render: () => <CookieBanner />,
};

// Cookie banner with custom translations
export const WithCustomTranslations: Story = {
  render: () => {
    // In a real application, you would use react-i18next's I18nextProvider
    return <CookieBanner />;
  },
};

// Cookie banner with custom styling
export const WithCustomStyling: Story = {
  render: () => (
    <div className="dark">
      <CookieBanner />
    </div>
  ),
};
