import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as DataTableStories from './data-table.stories';

<Meta of={DataTableStories} />

# DataTable

The DataTable component provides a simple and flexible way to display tabular data with basic functionality.

## Features

- Simple table with basic functionality
- Customizable columns with custom rendering
- Custom cell rendering
- Responsive design
- Accessibility support
- Dark mode compatible

## Usage

```tsx
import { DataTable } from '@kit/ui/data-table';

// Define your data type
interface Data {
  id: string;
  name: string;
  // ... other fields
}

// Define your columns
const columns: ColumnDef<Data>[] = [
  {
    id: 'name',
    accessorFn: (row: Data) => row.name,
    header: 'Name',
  },
  // ... other columns
];

// Your data
const data: Data[] = [
  // ... your data array
];

// Basic usage
function BasicExample() {
  return <DataTable columns={columns} data={data} />;
}
```

## Examples

### Basic Table

A simple table with basic functionality.

<Canvas of={DataTableStories.Basic} />

## Component API

### DataTable Props

<Controls of={DataTableStories.Basic} />

| Prop    | Type             | Description                    |
| ------- | ---------------- | ------------------------------ |
| columns | `ColumnDef<T>[]` | Array of column definitions    |
| data    | `T[]`            | Array of data items to display |

## Column Definition

Columns are defined using the `ColumnDef` type from @tanstack/react-table:

```tsx
interface ColumnDef<T> {
  id: string;
  accessorFn: (row: T) => any;
  header?: string | ((props: { column: Column<T> }) => React.ReactNode);
  cell?: (props: { row: Row<T> }) => React.ReactNode;
  // ... other properties
}
```

## Accessibility

The DataTable component follows WAI-ARIA guidelines for tables:

- Uses semantic HTML table elements
- Provides proper ARIA labels
- Supports keyboard navigation
- Screen reader friendly

## Guidelines

1. **Data Structure**

   - Ensure your data is properly structured and typed
   - Use consistent data formats across columns

2. **Performance**

   - Consider using virtual scrolling for large datasets
   - Use memoization for complex cell renderers

3. **Customization**

   - Use custom cell renderers for complex data display
   - Style using Tailwind CSS utilities

4. **Responsive Design**
   - Consider mobile view behavior
   - Use responsive classes for different screen sizes
   - Hide less important columns on smaller screens

## Best Practices

1. **Data Loading**

   - Show loading states during data fetching
   - Handle empty states gracefully
   - Provide error states for failed data loads

2. **User Experience**

   - Provide clear visual feedback for interactions
   - Keep table layout consistent

3. **Performance**

   - Avoid unnecessary re-renders
   - Optimize column definitions

4. **Accessibility**
   - Provide meaningful aria-labels
   - Ensure keyboard navigation works properly
   - Test with screen readers
