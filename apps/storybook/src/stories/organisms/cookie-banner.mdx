import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as CookieBannerStories from './cookie-banner.stories';

<Meta of={CookieBannerStories} />

# Cookie Banner

A GDPR-compliant cookie consent banner that allows users to accept or reject cookies. Built with accessibility and internationalization in mind.

## Features

- GDPR compliance
- Persistent consent storage
- Automatic consent management
- Internationalization support
- Accessible by default
- Dark mode compatible
- Responsive design
- Customizable styling

## Usage

```tsx
import { CookieBanner } from '@kit/ui/cookie-banner';

export default function Layout() {
  return (
    <>
      <CookieBanner />
      {/* Your app content */}
    </>
  );
}
```

## Examples

### Default

The default cookie banner with standard configuration.

<Canvas of={CookieBannerStories.Default} />

### With Custom Translations

Example of the cookie banner with custom translation strings.

<Canvas of={CookieBannerStories.WithCustomTranslations} />

### Dark Mode

The cookie banner in dark mode.

<Canvas of={CookieBannerStories.WithCustomStyling} />

## Component API

The CookieBanner component is designed to be used without props, as it handles all functionality internally.

<Controls />

## Accessibility

The Cookie Banner component follows WAI-ARIA guidelines for dialogs and alerts:

- Uses `role="dialog"` for the banner container
- Provides clear focus management
- Keyboard navigation support
- Screen reader announcements
- Focus trap within the banner
- Clear button labeling

## Design Guidelines

1. **Placement**

   - Always appears at the bottom of the viewport
   - Remains fixed until user interaction
   - Ensures content remains accessible

2. **Content Structure**

   - Clear and concise messaging
   - Explicit action buttons
   - Proper visual hierarchy
   - Sufficient contrast ratios

3. **Visual Feedback**
   - Clear button states
   - Smooth animations
   - Consistent with theme
   - Proper spacing and padding

## Best Practices

- Place the banner at the root of your application
- Initialize as early as possible
- Handle consent state changes appropriately
- Provide clear privacy policy links
- Use clear and concise language
- Test across different viewport sizes
- Ensure proper z-index management

## Technical Details

### Storage

- Uses `localStorage` for consent persistence
- Key: `cookie_consent_status`
- Values: `accepted`, `rejected`, `unknown`

### Dependencies

- Radix UI Dialog primitive
- react-i18next for translations
- Tailwind CSS for styling
- React 18+

### Performance Considerations

- Minimal bundle size impact
- Efficient state management
- Optimized re-renders
- Smooth animations

### Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome for Android)
- Graceful fallback for older browsers

## Related Components

- `Dialog` - Base component for modal interfaces
- `Alert` - For important notifications
- `Button` - For action triggers
- `Trans` - For internationalization
