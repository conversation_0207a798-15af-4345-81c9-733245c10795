import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as EnhancedDataTableStories from './enhanced-data-table.stories';

<Meta of={EnhancedDataTableStories} />

# Enhanced Data Table

The Enhanced Data Table component is a feature-rich table component built on top of Shadcn UI's table component. It provides additional functionality such as sorting, filtering, pagination, and row selection.

## Features

- Sortable columns
- Column filtering
- Pagination
- Row selection
- Responsive design
- Customizable styling
- Accessibility support

## Usage

```tsx
import { EnhancedDataTable } from '@kit/ui/enhanced-data-table';

// Define your columns
const columns = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
  },
  {
    id: 'email',
    header: 'Email',
    accessorKey: 'email',
  },
];

// Your data
const data = [
  { name: '<PERSON>', email: '<EMAIL>' },
  { name: '<PERSON>', email: '<EMAIL>' },
];

// Component implementation
export function MyTable() {
  return (
    <EnhancedDataTable
      columns={columns}
      data={data}
      pageSize={10}
      enableSelection
    />
  );
}
```

## Examples

### Basic

<Canvas of={EnhancedDataTableStories.Basic} />

### With Sorting

<Canvas of={EnhancedDataTableStories.WithSorting} />

### With Pagination

<Canvas of={EnhancedDataTableStories.WithPagination} />

### With Pagination and Sorting

<Canvas of={EnhancedDataTableStories.WithPaginationAndSorting} />

## Component API

<Controls />

## Props

| Prop                | Type                       | Default  | Description                     |
| ------------------- | -------------------------- | -------- | ------------------------------- |
| `columns`           | `ColumnDef[]`              | Required | Table column definitions        |
| `data`              | `T[]`                      | Required | Data to display in the table    |
| `pageSize`          | `number`                   | `10`     | Number of rows per page         |
| `enableSelection`   | `boolean`                  | `false`  | Enable row selection            |
| `onSelectionChange` | `(selection: T[]) => void` | -        | Callback when selection changes |
| `enableSorting`     | `boolean`                  | `true`   | Enable column sorting           |
| `enableFiltering`   | `boolean`                  | `true`   | Enable column filtering         |

## Accessibility

The Enhanced Data Table follows WAI-ARIA guidelines for tables and includes:

- Proper ARIA roles and attributes
- Keyboard navigation support
- Screen reader announcements for sorting and filtering
- Focus management

## Theming

The component uses Tailwind CSS for styling and inherits Shadcn UI's theming system. You can customize the appearance using Tailwind classes or by modifying the theme variables.
