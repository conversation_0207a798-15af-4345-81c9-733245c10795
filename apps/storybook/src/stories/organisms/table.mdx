import { <PERSON>vas, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as TableStories from './table.stories';

<Meta of={TableStories} />

# Table

A flexible and accessible table component built with Tailwind CSS. It provides a foundation for displaying tabular data with support for headers, footers, captions, and various styling options.

## Features

- Semantic HTML table structure
- Responsive design
- Dark mode compatible
- Customizable styling
- Header and footer support
- Caption support
- Hover states
- Accessibility support
- Flexible layout options
- Border customization
- Empty state handling

## Usage

```tsx
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';

function MyTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell>John <PERSON>e</TableCell>
          <TableCell><EMAIL></TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
}
```

## Examples

### Default Table

A basic table with header and body sections.

<Canvas of={TableStories.Default} />

### Table with Caption

A table with a descriptive caption.

<Canvas of={TableStories.WithCaption} />

### Table with Footer

A table that includes a footer section, useful for summaries or totals.

<Canvas of={TableStories.WithFooter} />

### Empty Table

A table displaying a message when no data is available.

<Canvas of={TableStories.Empty} />

### Table with Hover States

A table with interactive hover effects on rows.

<Canvas of={TableStories.WithHover} />

## Component API

### Table

The root table component that wraps all table elements.

#### Props

<Controls />

All components accept standard HTML attributes for their respective elements.

### Subcomponents

1. **TableHeader**

   - Wrapper for table header content
   - Uses `<thead>` element
   - Applies border styling to rows

2. **TableBody**

   - Container for table data rows
   - Uses `<tbody>` element
   - Handles border styling for last row

3. **TableFooter**

   - Container for footer content
   - Uses `<tfoot>` element
   - Applies background and font styling

4. **TableRow**

   - Represents a table row
   - Uses `<tr>` element
   - Supports hover and selected states

5. **TableHead**

   - Header cell component
   - Uses `<th>` element
   - Applies styling for header cells

6. **TableCell**

   - Data cell component
   - Uses `<td>` element
   - Handles padding and alignment

7. **TableCaption**
   - Caption for the table
   - Uses `<caption>` element
   - Applies caption-specific styling

## Accessibility

The Table component follows WAI-ARIA guidelines for tables:

- Uses semantic HTML table elements
- Provides proper header cell associations
- Supports screen readers
- Maintains keyboard navigation
- Includes proper ARIA attributes
- Handles focus management

## Design Guidelines

1. **Layout**

   - Maintain consistent column widths
   - Use appropriate spacing
   - Align content properly
   - Consider responsive behavior

2. **Typography**

   - Use clear, readable fonts
   - Maintain proper hierarchy
   - Ensure sufficient contrast
   - Consider text wrapping

3. **Visual Design**
   - Use borders effectively
   - Apply consistent styling
   - Consider hover states
   - Handle empty states

## Best Practices

1. **Content Organization**

   - Use clear column headers
   - Align data appropriately
   - Handle long content
   - Consider mobile views

2. **User Experience**

   - Provide clear feedback
   - Handle loading states
   - Show empty states
   - Consider sorting needs

3. **Implementation**
   - Use semantic markup
   - Handle responsive cases
   - Consider performance
   - Implement proper error states

## Technical Details

### Styling

The component uses Tailwind CSS with:

- Responsive classes
- Dark mode support
- Hover state styles
- Border customization
- Background colors
- Typography utilities

### Layout Options

1. **Fixed Layout**

```tsx
<Table className="table-fixed">{/* Table content */}</Table>
```

2. **Auto Layout**

```tsx
<Table className="table-auto">{/* Table content */}</Table>
```

3. **Responsive Scroll**

```tsx
<div className="overflow-x-auto">
  <Table>{/* Table content */}</Table>
</div>
```

### Customization

Common customizations include:

```tsx
// Custom cell alignment
<TableCell className="text-right">Content</TableCell>

// Custom padding
<TableCell className="p-4">Content</TableCell>

// Sticky header
<TableHeader className="sticky top-0 bg-background">
  {/* Header content */}
</TableHeader>

// Zebra striping
<TableRow className="even:bg-muted">
  {/* Row content */}
</TableRow>
```

## Related Components

- DataTable (`@kit/ui/data-table`)
- EnhancedDataTable (`@kit/ui/enhanced-data-table`)
- ScrollArea (`@kit/ui/scroll-area`)
- Card (`@kit/ui/card`)
