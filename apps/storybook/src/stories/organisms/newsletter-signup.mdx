import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as NewsletterSignupStories from './newsletter-signup.stories';

<Meta of={NewsletterSignupStories} />

# Newsletter Signup

The Newsletter Signup component is a form component designed for collecting email subscriptions. It includes form validation, error handling, and customizable styling.

## Features

- Email validation using Zod
- Customizable button text and placeholder
- Error message display
- Responsive design
- Dark mode compatible
- Accessible form controls

## Usage

```tsx
import { NewsletterSignup } from '@kit/ui/newsletter-signup';

export default function Page() {
  const handleSignup = async (data: { email: string }) => {
    // Handle signup logic
    await subscribeToNewsletter(data.email);
  };

  return (
    <NewsletterSignup
      onSignup={handleSignup}
      buttonText="Subscribe"
      placeholder="Enter your email"
    />
  );
}
```

## Examples

### Default

A basic newsletter signup form with default styling and text.

<Canvas of={NewsletterSignupStories.Default} />

### With Custom Text

Customized button text and placeholder.

<Canvas of={NewsletterSignupStories.WithCustomText} />

### With Custom Styling

Custom width and styling applied.

<Canvas of={NewsletterSignupStories.WithCustomStyling} />

## Component API

<Controls />

## Accessibility

The Newsletter Signup component follows accessibility best practices:

- Proper form labeling
- Error message announcements
- Keyboard navigation support
- ARIA attributes for form controls
- High contrast text colors

## Best Practices

1. Keep button text concise and action-oriented
2. Use clear placeholder text
3. Handle errors gracefully
4. Provide visual feedback during submission
5. Consider mobile input experience
6. Test form validation thoroughly

## Form Validation

The component uses Zod for form validation with the following schema:

```typescript
const NewsletterFormSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});
```

## Error Handling

The component displays validation errors using Shadcn UI's form components:

```tsx
<FormMessage /> // Displays validation errors
```

## Customization

The component can be customized using className props and by providing custom text:

```tsx
<NewsletterSignup
  className="custom-form-class"
  buttonText="Custom Button"
  placeholder="Custom Placeholder"
  onSignup={handleSignup}
/>
```
