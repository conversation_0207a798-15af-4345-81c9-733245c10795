import { Canvas, Controls, Meta } from '@storybook/addon-docs/blocks';

import * as StepperStories from './stepper.stories';

<Meta of={StepperStories} />

# Stepper

A versatile stepper component that visualizes progress through a sequence of steps. It supports multiple visual variants and is ideal for multi-step forms, wizards, and progress tracking.

## Features

- Three visual variants: numbers, dots, and progress bar
- Customizable step labels
- Responsive design
- Dark mode compatible
- Accessible by default
- Support for completed and current step states
- Internationalization support via Trans component
- Flexible layout options

## Usage

```tsx
import { Stepper } from '@kit/ui/stepper';

function MyComponent() {
  const steps = ['Step 1', 'Step 2', 'Step 3'];
  return <Stepper steps={steps} currentStep={1} variant="numbers" />;
}
```

## Examples

### Default Numbers Variant

The default numbers variant displays steps with numbered circles and labels.

<Canvas of={StepperStories.Default} />

### Dots Variant

A minimalist variant showing progress as a series of dots.

<Canvas of={StepperStories.Dots} />

### Progress Bar Variant

A linear progress bar showing step completion.

<Canvas of={StepperStories.ProgressBar} />

### Completed Steps

Example showing completed steps with appropriate styling.

<Canvas of={StepperStories.CompletedSteps} />

### First Step

Example showing the stepper at its initial step.

<Canvas of={StepperStories.FirstStep} />

### Last Step

Example showing the stepper at its final step.

<Canvas of={StepperStories.LastStep} />

## Component API

### Props

<Controls />

| Prop        | Type                             | Default   | Description                         |
| ----------- | -------------------------------- | --------- | ----------------------------------- |
| steps       | string[]                         | -         | Array of step labels                |
| currentStep | number                           | -         | Index of the current active step    |
| variant     | 'numbers' \| 'default' \| 'dots' | 'default' | Visual style variant of the stepper |

## Accessibility

The Stepper component follows WAI-ARIA guidelines:

- Uses semantic HTML elements
- Provides proper ARIA attributes for step status
- Maintains focus management
- Supports keyboard navigation
- Screen reader announcements for step changes

## Design Guidelines

1. **Step Labels**

   - Keep labels concise and clear
   - Use consistent terminology
   - Consider using icons with labels
   - Ensure sufficient contrast

2. **Visual Hierarchy**

   - Clearly distinguish current step
   - Show completed steps distinctly
   - Maintain consistent spacing
   - Use appropriate color contrast

3. **Responsive Design**
   - Adapt layout for different screen sizes
   - Consider mobile-first approach
   - Maintain readability at all breakpoints

## Best Practices

1. **Content Organization**

   - Group related steps together
   - Limit total number of steps (3-7 recommended)
   - Use clear, action-oriented labels
   - Show progress clearly

2. **User Experience**

   - Indicate current position clearly
   - Show completion status
   - Maintain consistent navigation
   - Provide clear feedback

3. **Implementation**
   - Use appropriate variant for context
   - Handle edge cases (first/last steps)
   - Consider loading states
   - Implement proper error handling

## Technical Details

### Variants

1. **Numbers (`variant="numbers"`)**

   - Displays numbered circles with labels
   - Best for sequential processes
   - Includes divider lines between steps

2. **Default (`variant="default"`)**

   - Shows progress bar with labels
   - Ideal for linear processes
   - Supports completion states

3. **Dots (`variant="dots"`)**
   - Minimal dot indicators
   - Best for compact displays
   - Supports hover states

### Styling

The component uses Tailwind CSS for styling with:

- Responsive classes
- Dark mode support
- Custom transitions
- Flexible layout options

### Integration

Common use cases include:

- Multi-step forms
- Onboarding flows
- Checkout processes
- Setup wizards
- Progress tracking

## Related Components

- MultiStepForm (`@kit/ui/multi-step-form`)
- Progress (`@kit/ui/progress`)
- Form (`@kit/ui/form`)
- Tabs (`@kit/ui/tabs`)
