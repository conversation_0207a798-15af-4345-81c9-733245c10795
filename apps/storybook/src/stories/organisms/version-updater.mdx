import { <PERSON><PERSON>, <PERSON>s, Meta } from '@storybook/addon-docs/blocks';

import * as VersionUpdaterStories from './version-updater.stories';

<Meta of={VersionUpdaterStories} />

# Version Updater

A client-side component that automatically checks for new application versions and prompts users to reload when updates are available.

## Features

- Automatic version checking at configurable intervals
- Non-intrusive update notification using AlertDialog
- Customizable check intervals
- Background polling with React Query
- Dark mode compatible
- Internationalization support
- Graceful handling of version changes

## Usage

```tsx
import { VersionUpdater } from '@kit/ui/version-updater';

function App() {
  return (
    <div>
      <VersionUpdater />
      {/* Your app content */}
    </div>
  );
}
```

## Examples

### Default Configuration

Uses the default interval time (120 seconds) to check for updates.

<Canvas of={VersionUpdaterStories.Default} />

### Custom Interval

Configures a custom interval time for version checks.

<Canvas of={VersionUpdaterStories.CustomInterval} />

### Version Change Simulation

Demonstrates the update notification when a new version is detected.

<Canvas of={VersionUpdaterStories.VersionChanged} />

## Component API

### Props

<Controls />

| Prop                 | Type   | Default | Description                                |
| -------------------- | ------ | ------- | ------------------------------------------ |
| intervalTimeInSecond | number | 120     | Interval in seconds between version checks |

## Technical Details

### Version Checking

The component:

1. Makes periodic requests to `/version` endpoint
2. Compares current version with previous version
3. Shows update dialog when versions differ
4. Provides option to reload the application

### Implementation Notes

- Uses React Query for efficient data fetching
- Implements background polling
- Maintains version state across component lifecycle
- Handles version comparison and update notification
- Supports environment variable configuration via `NEXT_PUBLIC_VERSION_UPDATER_REFETCH_INTERVAL_SECONDS`

### Accessibility

The Version Updater follows accessibility best practices:

- Uses AlertDialog for accessible notifications
- Provides keyboard navigation
- Includes proper ARIA attributes
- Maintains focus management
- Supports screen readers

## Design Guidelines

1. **Update Notification**

   - Clear and non-intrusive
   - Provides obvious action buttons
   - Maintains consistent styling
   - Respects dark mode preferences

2. **User Experience**
   - Background version checking
   - Optional update dismissal
   - Clear update messaging
   - Smooth reload transition

## Best Practices

1. **Implementation**

   - Place at app root level
   - Configure appropriate check intervals
   - Handle version endpoint errors
   - Consider user context

2. **Configuration**
   - Adjust interval based on update frequency
   - Consider bandwidth usage
   - Handle offline scenarios
   - Implement proper error boundaries

## Related Components

- AlertDialog (`@kit/ui/alert-dialog`)
- Button (`@kit/ui/button`)
- Trans (`@kit/ui/trans`)
