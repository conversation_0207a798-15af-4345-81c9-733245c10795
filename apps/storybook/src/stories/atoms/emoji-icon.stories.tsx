import type { Meta, StoryObj } from '@storybook/nextjs';

import { EmojiIcon } from '@kit/ui/dojo/atoms/emoji-icon';

const meta = {
  title: 'Atoms/EmojiIcon',
  component: EmojiIcon,
  tags: ['autodocs'],
  argTypes: {
    emojiName: {
      control: 'text',
      description: 'The emoji name (e.g., "smile", "heart", etc.)',
    },
    size: {
      control: 'number',
      description: 'Size in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof EmojiIcon>;

export default meta;
type Story = StoryObj<typeof EmojiIcon>;

export const Default: Story = {
  args: {
    emojiName: 'smile',
    size: 20,
  },
};

export const Large: Story = {
  args: {
    emojiName: 'heart',
    size: 40,
  },
};

export const Small: Story = {
  args: {
    emojiName: 'star',
    size: 16,
  },
};

export const WithCustomClass: Story = {
  args: {
    emojiName: 'rocket',
    size: 24,
    className: 'bg-muted p-2 rounded-full',
  },
};

export const Fallback: Story = {
  args: {
    emojiName: 'non_existent_emoji',
    size: 24,
  },
};

export const MultipleEmojis: Story = {
  render: () => (
    <div className="flex gap-4">
      <EmojiIcon emojiName="smile" size={24} />
      <EmojiIcon emojiName="heart" size={24} />
      <EmojiIcon emojiName="star" size={24} />
      <EmojiIcon emojiName="rocket" size={24} />
      <EmojiIcon emojiName="rainbow" size={24} />
    </div>
  ),
};
