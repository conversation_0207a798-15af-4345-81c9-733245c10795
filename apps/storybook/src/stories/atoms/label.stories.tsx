import type { Meta, StoryObj } from '@storybook/nextjs';

import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

/**
 * The Label component is used to provide accessible labels for form controls.
 * It's built on top of Radix UI Label and styled with Tailwind CSS.
 */
const meta = {
  title: 'Atoms/Label',
  component: Label,
  tags: ['autodocs'],
  argTypes: {
    htmlFor: {
      control: 'text',
      description: 'The ID of the form control this label is associated with',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the label',
    },
  },
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default label with input.
 */
export const Default: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email">Email</Label>
      <Input type="email" id="email" placeholder="Enter your email" />
    </div>
  ),
};

/**
 * Label with required field.
 */
export const Required: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="username">
        Username <span className="text-destructive">*</span>
      </Label>
      <Input type="text" id="username" required />
    </div>
  ),
};

/**
 * Label with disabled input.
 */
export const Disabled: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="disabled" className="text-muted-foreground">
        Disabled Field
      </Label>
      <Input type="text" id="disabled" disabled />
    </div>
  ),
};

/**
 * Label with helper text.
 */
export const WithHelperText: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="password">Password</Label>
      <Input type="password" id="password" />
      <p className="text-muted-foreground text-sm">
        Must be at least 8 characters long
      </p>
    </div>
  ),
};

/**
 * Different label positions.
 */
export const Positions: Story = {
  render: () => (
    <div className="grid w-full max-w-sm gap-4">
      <div className="grid gap-1.5">
        <Label htmlFor="top">Top Position (Default)</Label>
        <Input type="text" id="top" />
      </div>
      <div className="flex items-center gap-2">
        <Label htmlFor="left">Left Position</Label>
        <Input type="text" id="left" />
      </div>
      <div className="flex flex-row-reverse items-center gap-2">
        <Label htmlFor="right">Right Position</Label>
        <Input type="text" id="right" />
      </div>
    </div>
  ),
};

/**
 * Label with error state.
 */
export const Error: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="error" className="text-destructive">
        Invalid Field
      </Label>
      <Input
        type="text"
        id="error"
        className="border-destructive focus-visible:ring-destructive"
      />
      <p className="text-destructive text-sm">This field is required</p>
    </div>
  ),
};

/**
 * Label with success state.
 */
export const Success: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="success" className="text-success">
        Valid Field
      </Label>
      <Input
        type="text"
        id="success"
        className="border-success focus-visible:ring-success"
        defaultValue="Correct input"
      />
      <p className="text-success text-sm">Looks good!</p>
    </div>
  ),
};
