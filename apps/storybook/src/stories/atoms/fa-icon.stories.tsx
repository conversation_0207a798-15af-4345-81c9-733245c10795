import type { Meta, StoryObj } from '@storybook/nextjs';

import { FaIcon } from '@kit/ui/dojo/atoms/fa-icon';

const meta = {
  title: 'Atoms/FaIcon',
  component: FaIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    icon: {
      control: 'text',
      description: 'Icon name in the format "style/name" (e.g., "solid/user")',
    },
    size: {
      control: 'select',
      options: [
        'xs',
        'sm',
        'lg',
        '1x',
        '2x',
        '3x',
        '4x',
        '5x',
        '6x',
        '7x',
        '8x',
        '9x',
        '10x',
      ],
      description: 'Size of the icon',
    },
    spin: {
      control: 'boolean',
      description: 'Whether the icon should spin',
    },
    pulse: {
      control: 'boolean',
      description: 'Whether the icon should pulse',
    },
    border: {
      control: 'boolean',
      description: 'Whether the icon should have a border',
    },
    fixedWidth: {
      control: 'boolean',
      description: 'Whether the icon should have a fixed width',
    },
    flip: {
      control: 'select',
      options: ['horizontal', 'vertical', 'both'],
      description: 'Flip the icon',
    },
    rotation: {
      control: 'select',
      options: [90, 180, 270],
      description: 'Rotate the icon',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof FaIcon>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic usage of the FaIcon component with a solid icon
 */
export const Solid: Story = {
  args: {
    icon: 'solid/user',
    size: '2x',
  },
};

/**
 * Regular style icon
 */
export const Regular: Story = {
  args: {
    icon: 'regular/bell',
    size: '2x',
  },
};

/**
 * Brand icon
 */
export const Brand: Story = {
  args: {
    icon: 'brands/github',
    size: '2x',
  },
};

/**
 * Different sizes
 */
export const Sizes: Story = {
  args: {
    icon: 'solid/star',
  },
  render: () => (
    <div className="flex items-end gap-4">
      <FaIcon icon="solid/star" size="xs" />
      <FaIcon icon="solid/star" size="sm" />
      <FaIcon icon="solid/star" size="lg" />
      <FaIcon icon="solid/star" size="1x" />
      <FaIcon icon="solid/star" size="2x" />
      <FaIcon icon="solid/star" size="3x" />
    </div>
  ),
};

/**
 * Spinning icon
 */
export const Spinning: Story = {
  args: {
    icon: 'solid/spinner',
    size: '3x',
    spin: true,
  },
};

/**
 * Pulsing icon
 */
export const Pulsing: Story = {
  args: {
    icon: 'solid/spinner',
    size: '3x',
    pulse: true,
  },
};

/**
 * Icon with border
 */
export const WithBorder: Story = {
  args: {
    icon: 'solid/check',
    size: '2x',
    border: true,
  },
};

/**
 * Fixed width icons
 */
export const FixedWidth: Story = {
  args: {
    icon: 'solid/home',
  },
  render: () => (
    <div className="flex flex-col items-start gap-2 text-lg">
      <div>
        <FaIcon icon="solid/home" fixedWidth />
        <span className="ml-2">Home</span>
      </div>
      <div>
        <FaIcon icon="solid/user" fixedWidth />
        <span className="ml-2">Profile</span>
      </div>
      <div>
        <FaIcon icon="solid/cog" fixedWidth />
        <span className="ml-2">Settings</span>
      </div>
    </div>
  ),
};

/**
 * Flipped icons
 */
export const Flipped: Story = {
  args: {
    icon: 'solid/arrow-right',
  },
  render: () => (
    <div className="flex gap-4">
      <FaIcon icon="solid/arrow-right" size="2x" />
      <FaIcon icon="solid/arrow-right" size="2x" flip="horizontal" />
      <FaIcon icon="solid/arrow-up" size="2x" />
      <FaIcon icon="solid/arrow-up" size="2x" flip="vertical" />
      <FaIcon icon="solid/arrow-up" size="2x" flip="both" />
    </div>
  ),
};

/**
 * Rotated icons
 */
export const Rotated: Story = {
  args: {
    icon: 'solid/arrow-up',
  },
  render: () => (
    <div className="flex gap-4">
      <FaIcon icon="solid/arrow-up" size="2x" />
      <FaIcon icon="solid/arrow-up" size="2x" rotation={90} />
      <FaIcon icon="solid/arrow-up" size="2x" rotation={180} />
      <FaIcon icon="solid/arrow-up" size="2x" rotation={270} />
    </div>
  ),
};

/**
 * Colored icons using Tailwind classes
 */
export const Colored: Story = {
  args: {
    icon: 'solid/heart',
  },
  render: () => (
    <div className="flex gap-4">
      <FaIcon icon="solid/heart" size="2x" className="text-red-500" />
      <FaIcon icon="solid/star" size="2x" className="text-yellow-500" />
      <FaIcon icon="solid/circle-check" size="2x" className="text-green-500" />
      <FaIcon icon="solid/circle-info" size="2x" className="text-blue-500" />
      <FaIcon
        icon="solid/circle-exclamation"
        size="2x"
        className="text-orange-500"
      />
    </div>
  ),
};

/**
 * Common social media icons
 */
export const SocialIcons: Story = {
  args: {
    icon: 'brands/github',
  },
  render: () => (
    <div className="flex gap-4">
      <FaIcon icon="brands/github" size="2x" />
      <FaIcon icon="brands/twitter" size="2x" className="text-blue-400" />
      <FaIcon icon="brands/linkedin" size="2x" className="text-blue-700" />
      <FaIcon icon="brands/instagram" size="2x" className="text-pink-500" />
      <FaIcon icon="brands/youtube" size="2x" className="text-red-600" />
    </div>
  ),
};

// Demonstrate fallback behavior with invalid icons
export const Fallbacks: Story = {
  args: {
    icon: 'invalid/format',
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <FaIcon icon="invalid/format" />
        <span className="text-muted-foreground text-sm">Invalid format</span>
      </div>
      <div className="flex items-center gap-2">
        <FaIcon icon="unknown/star" />
        <span className="text-muted-foreground text-sm">Unknown style</span>
      </div>
      <div className="flex items-center gap-2">
        <FaIcon icon="solid/nonexistent" />
        <span className="text-muted-foreground text-sm">Nonexistent icon</span>
      </div>
      <div className="flex items-center gap-2">
        <FaIcon icon="solid/" />
        <span className="text-muted-foreground text-sm">Missing icon name</span>
      </div>
    </div>
  ),
};
