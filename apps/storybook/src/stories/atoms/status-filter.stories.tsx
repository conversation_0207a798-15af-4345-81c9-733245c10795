import type { Meta, StoryObj } from '@storybook/nextjs';

import { StatusFilter } from '@kit/ui/dojo/atoms/status-filter';

const meta = {
  title: 'Atoms/StatusFilter',
  component: StatusFilter,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof StatusFilter>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    options: [
      { value: 'all', label: 'All' },
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
    ],
    paramName: 'status',
    defaultValue: 'all',
    placeholder: 'Filter by status',
    className: 'w-[180px]',
  },
};

export const CustomOptions: Story = {
  args: {
    options: [
      { value: 'all', label: 'All Posts' },
      { value: 'published', label: 'Published' },
      { value: 'draft', label: 'Draft' },
      { value: 'archived', label: 'Archived' },
    ],
    paramName: 'postStatus',
    defaultValue: 'all',
    placeholder: 'Filter posts',
    className: 'w-[200px]',
  },
};

export const Minimal: Story = {
  args: {
    options: [
      { value: 'all', label: 'All' },
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
  },
};
