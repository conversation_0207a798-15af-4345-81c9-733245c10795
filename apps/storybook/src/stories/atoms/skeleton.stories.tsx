import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { Skeleton } from '@kit/ui/skeleton';

/**
 * The Skeleton component is used to show a placeholder while content is loading.
 * It provides a pulsing animation to indicate loading state.
 */
const meta = {
  title: 'Atoms/Skeleton',
  component: Skeleton,
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the skeleton',
    },
  },
} satisfies Meta<typeof Skeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default skeleton with basic styling.
 */
export const Default: Story = {
  render: () => <Skeleton className="h-4 w-[250px]" />,
};

/**
 * Different shapes and sizes of skeletons.
 */
export const Shapes: Story = {
  render: () => (
    <div className="flex flex-col space-y-4">
      <Skeleton className="h-4 w-[250px]" />
      <Skeleton className="h-8 w-[350px]" />
      <Skeleton className="h-12 w-[300px]" />
      <Skeleton className="h-32 w-[400px]" />
    </div>
  ),
};

/**
 * Circular skeleton for avatars or icons.
 */
export const Circle: Story = {
  render: () => <Skeleton className="size-12 rounded-full" />,
};

/**
 * Card skeleton with multiple elements.
 */
export const Card: Story = {
  render: () => (
    <div className="space-y-3">
      <Skeleton className="h-[125px] w-[250px] rounded-xl" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-[250px]" />
        <Skeleton className="h-4 w-[200px]" />
      </div>
    </div>
  ),
};

/**
 * Table skeleton showing loading state for tabular data.
 */
export const Table: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4">
            <Skeleton className="size-8 rounded-full" />
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-4 w-[100px]" />
          </div>
        ))}
      </div>
    </div>
  ),
};

/**
 * Form skeleton showing loading state for form elements.
 */
export const Form: Story = {
  render: () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-20 w-full" />
      </div>
      <Skeleton className="h-10 w-[100px]" />
    </div>
  ),
};

/**
 * Custom styled skeleton with different background color.
 */
export const CustomStyling: Story = {
  render: () => (
    <Skeleton className="h-4 w-[250px] bg-blue-500/10 dark:bg-blue-200/10" />
  ),
};
