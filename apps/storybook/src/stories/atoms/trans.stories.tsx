import type { Meta, StoryObj } from '@storybook/nextjs';

import { Trans } from '@kit/ui/trans';

const meta = {
  title: 'Atoms/Trans',
  component: Trans,
  tags: ['autodocs'],
  argTypes: {
    i18nKey: {
      control: 'text',
      description: 'Translation key to use',
    },
    values: {
      control: 'object',
      description: 'Values to interpolate into the translation',
    },
    components: {
      control: 'object',
      description: 'Components to use in the translation',
    },
  },
} satisfies Meta<typeof Trans>;

export default meta;
type Story = StoryObj<typeof Trans>;

export const Default: Story = {
  args: {
    i18nKey: 'common.welcome',
    children: 'Welcome to our application',
  },
};

export const WithVariables: Story = {
  args: {
    i18nKey: 'common.greeting',
    values: {
      name: 'John',
    },
    children: 'Hello, {{name}}!',
  },
};

export const WithComponents: Story = {
  args: {
    i18nKey: 'common.link',
    components: {
      a: <a href="#" className="text-primary hover:underline" />,
    },
    children: 'Click <a>here</a> to learn more',
  },
};

export const WithHtml: Story = {
  args: {
    i18nKey: 'common.formatted',
    components: {
      bold: <strong className="font-bold" />,
      em: <em className="italic" />,
    },
    children: 'This text is <bold>bold</bold> and <em>emphasized</em>',
  },
};

export const WithCount: Story = {
  args: {
    i18nKey: 'common.items',
    values: {
      count: 5,
    },
    children: '{{count}} items',
  },
};

export const WithContext: Story = {
  args: {
    i18nKey: 'common.context',
    context: 'male',
    children: 'He is a developer',
  },
};

export const ComplexExample: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <Trans i18nKey="common.welcome">Welcome to our application</Trans>
      </div>
      <div>
        <Trans i18nKey="common.greeting" values={{ name: 'John' }}>
          Hello, {'{{name}}'}!
        </Trans>
      </div>
      <div>
        <Trans
          i18nKey="common.formatted"
          components={{
            bold: <strong className="font-bold" />,
            em: <em className="italic" />,
          }}
        >
          This text is <strong>bold</strong> and <em>emphasized</em>
        </Trans>
      </div>
    </div>
  ),
};
