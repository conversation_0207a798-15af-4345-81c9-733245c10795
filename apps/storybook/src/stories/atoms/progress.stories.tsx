import { useEffect, useState } from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Progress } from '@kit/ui/progress';

/**
 * The Progress component is used to show the completion status of an operation.
 * It's built on top of Radix UI Progress and styled with Tailwind CSS.
 */
const meta = {
  title: 'Atoms/Progress',
  component: Progress,
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: { type: 'range', min: 0, max: 100, step: 1 },
      description: 'The progress value (0-100)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the progress bar',
    },
  },
} satisfies Meta<typeof Progress>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default progress bar with a static value.
 */
export const Default: Story = {
  args: {
    value: 40,
  },
};

/**
 * Progress bar in indeterminate state.
 */
export const Indeterminate: Story = {
  render: () => {
    const [progress, setProgress] = useState(10);

    useEffect(() => {
      const timer = setInterval(() => {
        setProgress((prevProgress) =>
          prevProgress >= 100 ? 10 : prevProgress + 10,
        );
      }, 500);

      return () => {
        clearInterval(timer);
      };
    }, []);

    return <Progress value={progress} />;
  },
};

/**
 * Progress bar with different sizes.
 */
export const Sizes: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <Progress value={75} className="h-1" />
      <Progress value={75} className="h-2" />
      <Progress value={75} className="h-3" />
      <Progress value={75} className="h-4" />
    </div>
  ),
};

/**
 * Progress bar with custom colors.
 */
export const CustomColors: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <Progress value={75} className="bg-blue-200 [&>div]:bg-blue-500" />
      <Progress value={50} className="bg-green-200 [&>div]:bg-green-500" />
      <Progress value={25} className="bg-red-200 [&>div]:bg-red-500" />
    </div>
  ),
};

/**
 * Progress bar with label.
 */
export const WithLabel: Story = {
  render: () => {
    const value = 66;
    return (
      <div className="grid gap-2">
        <div className="flex justify-between">
          <span className="text-sm font-medium">Progress</span>
          <span className="text-muted-foreground text-sm">{value}%</span>
        </div>
        <Progress value={value} />
      </div>
    );
  },
};

/**
 * Progress bar with steps.
 */
export const WithSteps: Story = {
  render: () => {
    const steps = [
      { name: 'Step 1', value: 100 },
      { name: 'Step 2', value: 100 },
      { name: 'Step 3', value: 50 },
      { name: 'Step 4', value: 0 },
    ];
    const totalSteps = steps.length;
    const completedSteps = steps.filter((step) => step.value === 100).length;
    const progress = (completedSteps / totalSteps) * 100;

    return (
      <div className="grid gap-4">
        <Progress value={progress} />
        <div className="grid gap-2">
          {steps.map((step) => (
            <div key={step.name} className="grid gap-1">
              <div className="flex justify-between">
                <span className="text-sm font-medium">{step.name}</span>
                <span className="text-muted-foreground text-sm">
                  {step.value}%
                </span>
              </div>
              <Progress value={step.value} className="h-1" />
            </div>
          ))}
        </div>
      </div>
    );
  },
};
