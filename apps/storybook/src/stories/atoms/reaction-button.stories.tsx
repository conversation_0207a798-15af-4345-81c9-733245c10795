import type { Meta, StoryObj } from '@storybook/nextjs';
import { Heart, Star, ThumbsUp } from 'lucide-react';

import { EmojiIcon } from '@kit/ui/dojo/atoms/emoji-icon';
import { ReactionButton } from '@kit/ui/dojo/atoms/reaction-button';

const meta = {
  title: 'Atoms/ReactionButton',
  component: ReactionButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ReactionButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    icon: <Heart className="h-4 w-4" />,
    count: 5,
  },
};

export const Active: Story = {
  args: {
    icon: <ThumbsUp className="h-4 w-4" />,
    count: 10,
    isActive: true,
  },
};

export const Loading: Story = {
  args: {
    icon: <Star className="h-4 w-4" />,
    count: 3,
    isLoading: true,
  },
};

export const Disabled: Story = {
  args: {
    icon: <Heart className="h-4 w-4" />,
    count: 7,
    disabled: true,
  },
};

export const NoCount: Story = {
  args: {
    icon: <Heart className="h-4 w-4" />,
  },
};

export const WithEmoji: Story = {
  args: {
    icon: <EmojiIcon emojiName="heart" size={16} />,
    count: 12,
  },
};

export const WithEmojiActive: Story = {
  args: {
    icon: <EmojiIcon emojiName="tada" size={16} />,
    count: 8,
    isActive: true,
  },
};
