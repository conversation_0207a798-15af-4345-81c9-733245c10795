import type { Meta, StoryObj } from '@storybook/nextjs';

import { Switch } from '@kit/ui/switch';

/**
 * The Switch component is used to toggle between two states.
 * It provides a visual toggle that can be either on or off.
 */
const meta = {
  title: 'Atoms/Switch',
  component: Switch,
  tags: ['autodocs'],
  argTypes: {
    checked: {
      control: 'boolean',
      description: 'The controlled checked state of the switch',
    },
    defaultChecked: {
      control: 'boolean',
      description: 'The default checked state when initially rendered',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the switch is disabled',
    },
    required: {
      control: 'boolean',
      description: 'Whether the switch is required in a form',
    },
    onCheckedChange: {
      description: 'Event handler called when the checked state changes',
    },
  },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default switch with basic styling.
 */
export const Default: Story = {
  render: () => <Switch />,
};

/**
 * Switch in a checked state.
 */
export const Checked: Story = {
  render: () => <Switch defaultChecked />,
};

/**
 * Switch in a disabled state.
 */
export const Disabled: Story = {
  render: () => <Switch disabled />,
};

/**
 * Switch in a disabled and checked state.
 */
export const DisabledChecked: Story = {
  render: () => <Switch disabled defaultChecked />,
};

/**
 * Switch with a required state for form validation.
 */
export const Required: Story = {
  render: () => <Switch required />,
};

/**
 * Switch with a custom label using aria-label.
 */
export const WithAriaLabel: Story = {
  render: () => <Switch aria-label="Toggle feature" />,
};

/**
 * Switch with an associated form label.
 */
export const WithLabel: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Switch id="airplane-mode" />
      <label
        htmlFor="airplane-mode"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        Airplane Mode
      </label>
    </div>
  ),
};

/**
 * Switch with loading state simulation.
 */
export const Loading: Story = {
  render: () => {
    return (
      <div className="flex items-center space-x-2">
        <Switch disabled className="data-[state=checked]:bg-primary/50" />
        <span className="text-muted-foreground text-sm">Loading...</span>
      </div>
    );
  },
};

/**
 * Switch with custom styling.
 */
export const CustomStyling: Story = {
  render: () => (
    <Switch className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500" />
  ),
};
