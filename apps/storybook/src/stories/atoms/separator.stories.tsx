import type { Meta, StoryObj } from '@storybook/nextjs';

import { Separator } from '@kit/ui/separator';

const meta = {
  title: 'Atoms/Separator',
  component: Separator,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: 'radio',
      options: ['horizontal', 'vertical'],
      description: 'The orientation of the separator',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
    decorative: {
      control: 'boolean',
      description: 'Whether the separator is purely decorative',
    },
  },
} satisfies Meta<typeof Separator>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default horizontal separator
export const Default: Story = {
  args: {
    orientation: 'horizontal',
    className: 'my-4',
  },
  render: (args) => (
    <div className="w-full max-w-md space-y-4">
      <div className="text-muted-foreground text-sm">Above separator</div>
      <Separator {...args} />
      <div className="text-muted-foreground text-sm">Below separator</div>
    </div>
  ),
};

// Vertical separator with text
export const Vertical: Story = {
  args: {
    orientation: 'vertical',
    className: 'mx-4 h-6',
  },
  render: (args) => (
    <div className="flex h-10 items-center">
      <div className="text-muted-foreground text-sm">Left</div>
      <Separator {...args} />
      <div className="text-muted-foreground text-sm">Right</div>
    </div>
  ),
};

// Decorative separator
export const Decorative: Story = {
  args: {
    decorative: true,
    className: 'my-4',
  },
  render: (args) => (
    <div className="w-full max-w-md space-y-4">
      <div className="text-muted-foreground text-sm">
        Decorative separator below
      </div>
      <Separator {...args} />
    </div>
  ),
};

// Separator in a list context
export const InList: Story = {
  render: () => (
    <div className="w-full max-w-md space-y-2">
      <h4 className="text-sm font-medium leading-none">Navigation</h4>
      <Separator className="my-2" />
      <div className="flex h-5 items-center space-x-4 text-sm">
        <div>Home</div>
        <Separator orientation="vertical" className="h-4" />
        <div>About</div>
        <Separator orientation="vertical" className="h-4" />
        <div>Contact</div>
      </div>
    </div>
  ),
};
