import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Calendar, MessageCircle, ThumbsUp } from 'lucide-react';

import { PostMetadata } from '@kit/ui/dojo/atoms/post-metadata';

const meta = {
  title: 'Atoms/PostMetadata',
  component: PostMetadata,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PostMetadata>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    items: [
      {
        icon: <Calendar className="text-muted-foreground h-4 w-4" />,
        text: '2024-03-20',
        label: 'Posted on',
      },
      {
        icon: <MessageCircle className="text-muted-foreground h-4 w-4" />,
        text: '5 comments',
        label: 'Number of comments',
      },
      {
        icon: <ThumbsUp className="text-muted-foreground h-4 w-4" />,
        text: '10 reactions',
        label: 'Number of reactions',
      },
    ],
  },
};

export const SingleItem: Story = {
  args: {
    items: [
      {
        icon: <Calendar className="text-muted-foreground h-4 w-4" />,
        text: '2024-03-20',
        label: 'Posted on',
      },
    ],
  },
};

export const CustomStyle: Story = {
  args: {
    className: 'bg-accent p-2 rounded',
    items: [
      {
        icon: <Calendar className="text-muted-foreground h-4 w-4" />,
        text: '2024-03-20',
      },
      {
        icon: <MessageCircle className="text-muted-foreground h-4 w-4" />,
        text: '5 comments',
      },
    ],
  },
};
