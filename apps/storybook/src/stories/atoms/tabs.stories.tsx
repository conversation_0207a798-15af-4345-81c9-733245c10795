import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@kit/ui/tabs';

const meta = {
  title: 'Atoms/Tabs',
  component: Tabs,
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'text',
      description: 'The default selected tab value',
    },
    value: {
      control: 'text',
      description: 'The controlled value of the tab to activate',
    },
    onValueChange: {
      description: 'Event handler called when the value changes',
    },
    orientation: {
      control: 'radio',
      options: ['horizontal', 'vertical'],
      description: 'The orientation of the component',
    },
    dir: {
      control: 'radio',
      options: ['ltr', 'rtl'],
      description: 'The reading direction of the tabs',
    },
    activationMode: {
      control: 'radio',
      options: ['automatic', 'manual'],
      description: 'When tabs are activated (automatic or manual)',
    },
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Tabs defaultValue="account" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="account">Account</TabsTrigger>
        <TabsTrigger value="password">Password</TabsTrigger>
        <TabsTrigger value="settings">Settings</TabsTrigger>
      </TabsList>
      <TabsContent value="account">
        <div className="p-4 text-sm">
          Make changes to your account settings here.
        </div>
      </TabsContent>
      <TabsContent value="password">
        <div className="p-4 text-sm">Change your password here.</div>
      </TabsContent>
      <TabsContent value="settings">
        <div className="p-4 text-sm">Edit your preferences here.</div>
      </TabsContent>
    </Tabs>
  ),
};

export const Vertical: Story = {
  render: () => (
    <Tabs defaultValue="account" orientation="vertical" className="w-[400px]">
      <TabsList className="w-[200px] flex-col space-y-2">
        <TabsTrigger value="account">Account</TabsTrigger>
        <TabsTrigger value="password">Password</TabsTrigger>
        <TabsTrigger value="settings">Settings</TabsTrigger>
      </TabsList>
      <TabsContent value="account">
        <div className="p-4 text-sm">
          Make changes to your account settings here.
        </div>
      </TabsContent>
      <TabsContent value="password">
        <div className="p-4 text-sm">Change your password here.</div>
      </TabsContent>
      <TabsContent value="settings">
        <div className="p-4 text-sm">Edit your preferences here.</div>
      </TabsContent>
    </Tabs>
  ),
};

export const WithIcons: Story = {
  render: () => (
    <Tabs defaultValue="music" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="music" className="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M9 18V5l12-2v13" />
            <circle cx="6" cy="18" r="3" />
            <circle cx="18" cy="16" r="3" />
          </svg>
          Music
        </TabsTrigger>
        <TabsTrigger value="photos" className="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2Z" />
            <circle cx="8.5" cy="8.5" r="1.5" />
            <path d="m21 15-5-5L5 21" />
          </svg>
          Photos
        </TabsTrigger>
        <TabsTrigger value="videos" className="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="m22 8-6 4 6 4V8Z" />
            <rect width="14" height="12" x="2" y="6" rx="2" ry="2" />
          </svg>
          Videos
        </TabsTrigger>
      </TabsList>
      <TabsContent value="music">
        <div className="p-4 text-sm">Your music library.</div>
      </TabsContent>
      <TabsContent value="photos">
        <div className="p-4 text-sm">Your photo gallery.</div>
      </TabsContent>
      <TabsContent value="videos">
        <div className="p-4 text-sm">Your video collection.</div>
      </TabsContent>
    </Tabs>
  ),
};

export const Disabled: Story = {
  render: () => (
    <Tabs defaultValue="active" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="active">Active</TabsTrigger>
        <TabsTrigger value="disabled" disabled>
          Disabled
        </TabsTrigger>
        <TabsTrigger value="pending">Pending</TabsTrigger>
      </TabsList>
      <TabsContent value="active">
        <div className="p-4 text-sm">This tab is active.</div>
      </TabsContent>
      <TabsContent value="disabled">
        <div className="p-4 text-sm">This tab is disabled.</div>
      </TabsContent>
      <TabsContent value="pending">
        <div className="p-4 text-sm">This tab is pending.</div>
      </TabsContent>
    </Tabs>
  ),
};

export const CustomStyling: Story = {
  render: () => (
    <Tabs defaultValue="tab1" className="w-[400px]">
      <TabsList className="bg-primary p-1">
        <TabsTrigger
          value="tab1"
          className="data-[state=active]:text-primary data-[state=active]:bg-white"
        >
          Tab 1
        </TabsTrigger>
        <TabsTrigger
          value="tab2"
          className="data-[state=active]:text-primary data-[state=active]:bg-white"
        >
          Tab 2
        </TabsTrigger>
        <TabsTrigger
          value="tab3"
          className="data-[state=active]:text-primary data-[state=active]:bg-white"
        >
          Tab 3
        </TabsTrigger>
      </TabsList>
      <TabsContent value="tab1" className="border-primary border-2">
        <div className="p-4 text-sm">Content for Tab 1</div>
      </TabsContent>
      <TabsContent value="tab2" className="border-primary border-2">
        <div className="p-4 text-sm">Content for Tab 2</div>
      </TabsContent>
      <TabsContent value="tab3" className="border-primary border-2">
        <div className="p-4 text-sm">Content for Tab 3</div>
      </TabsContent>
    </Tabs>
  ),
};

export const WithForms: Story = {
  render: () => (
    <Tabs defaultValue="login" className="w-[400px]">
      <TabsList>
        <TabsTrigger value="login">Login</TabsTrigger>
        <TabsTrigger value="register">Register</TabsTrigger>
      </TabsList>
      <TabsContent value="login">
        <form className="space-y-4 p-4">
          <div>
            <label className="text-sm font-medium">Email</label>
            <input
              type="email"
              className="mt-1 w-full rounded-md border p-2"
              placeholder="Enter your email"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Password</label>
            <input
              type="password"
              className="mt-1 w-full rounded-md border p-2"
              placeholder="Enter your password"
            />
          </div>
          <button
            type="submit"
            className="bg-primary w-full rounded-md p-2 text-white"
          >
            Login
          </button>
        </form>
      </TabsContent>
      <TabsContent value="register">
        <form className="space-y-4 p-4">
          <div>
            <label className="text-sm font-medium">Name</label>
            <input
              type="text"
              className="mt-1 w-full rounded-md border p-2"
              placeholder="Enter your name"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Email</label>
            <input
              type="email"
              className="mt-1 w-full rounded-md border p-2"
              placeholder="Enter your email"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Password</label>
            <input
              type="password"
              className="mt-1 w-full rounded-md border p-2"
              placeholder="Choose a password"
            />
          </div>
          <button
            type="submit"
            className="bg-primary w-full rounded-md p-2 text-white"
          >
            Register
          </button>
        </form>
      </TabsContent>
    </Tabs>
  ),
};
