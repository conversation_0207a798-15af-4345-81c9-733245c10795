import type { Meta, StoryObj } from '@storybook/nextjs';

import {
  CourseStats,
  createPositiveNumber,
} from '@kit/ui/dojo/atoms/course-stats';

/** @type {Meta<typeof CourseStats>} */
const meta = {
  title: 'Course/Atoms/CourseStats',
  component: CourseStats,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'Displays course statistics including duration and lesson count. Duration must be in "Xh Ym" or "Ym" format, and lesson count must be positive.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseStats>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    duration: '2h 30m',
    lessonCount: createPositiveNumber(12),
  },
};

export const ShortCourse: Story = {
  args: {
    duration: '30m', // Minimum valid duration
    lessonCount: createPositiveNumber(1), // Minimum valid lesson count
  },
};

export const LongCourse: Story = {
  args: {
    duration: '999h 59m', // Testing large duration
    lessonCount: createPositiveNumber(999), // Testing large lesson count
  },
};

export const SingleLesson: Story = {
  args: {
    duration: '15m',
    lessonCount: createPositiveNumber(1), // Edge case: minimum valid lesson count
  },
};

export const HoursOnly: Story = {
  args: {
    duration: '1h', // Testing hours without minutes
    lessonCount: createPositiveNumber(5),
  },
};

export const MinutesOnly: Story = {
  args: {
    duration: '45m', // Testing minutes without hours
    lessonCount: createPositiveNumber(3),
  },
};
