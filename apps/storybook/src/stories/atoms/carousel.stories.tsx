import Image from 'next/image';

import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@kit/ui/carousel';

const meta = {
  title: 'Atoms/Carousel',
  component: Carousel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Carousel>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <div className="w-full max-w-xs">
      <Carousel>
        <CarouselContent>
          {Array.from({ length: 5 }).map((_, index) => (
            <CarouselItem key={index}>
              <div className="p-1">
                <div className="bg-secondary flex aspect-square items-center justify-center rounded-md p-6">
                  <span className="text-4xl font-semibold">{index + 1}</span>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  ),
};

export const WithImages: Story = {
  render: () => (
    <div className="w-full max-w-md">
      <Carousel>
        <CarouselContent>
          {[
            'https://picsum.photos/id/1/400/300',
            'https://picsum.photos/id/2/400/300',
            'https://picsum.photos/id/3/400/300',
            'https://picsum.photos/id/4/400/300',
          ].map((src, index) => (
            <CarouselItem key={index}>
              <div className="p-1">
                <Image
                  src={src}
                  alt={`Slide ${index + 1}`}
                  className="h-full w-full rounded-md object-cover"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-2" />
        <CarouselNext className="right-2" />
      </Carousel>
    </div>
  ),
};

export const MultipleItems: Story = {
  render: () => (
    <div className="w-full max-w-md">
      <Carousel
        opts={{
          align: 'start',
          loop: true,
        }}
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {Array.from({ length: 10 }).map((_, index) => (
            <CarouselItem
              key={index}
              className="basis-1/3 pl-2 md:basis-1/4 md:pl-4"
            >
              <div className="p-1">
                <div className="bg-secondary flex aspect-square items-center justify-center rounded-md p-2">
                  <span className="text-2xl font-semibold">{index + 1}</span>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  ),
};
