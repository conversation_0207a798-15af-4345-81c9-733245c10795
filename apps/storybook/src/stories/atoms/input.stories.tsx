import type { Meta, StoryObj } from '@storybook/nextjs';
import { Mail, Search as SearchIcon } from 'lucide-react';

import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

/**
 * The Input component is used for collecting user input in a form.
 * It supports different types and states, and can be customized using Tailwind CSS.
 */
const meta = {
  title: 'Atoms/Input',
  component: Input,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'number', 'search', 'tel', 'url'],
      description: 'The type of input',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    required: {
      control: 'boolean',
      description: 'Whether the input is required',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the input',
    },
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default text input.
 */
export const Default: Story = {
  args: {
    type: 'text',
    placeholder: 'Enter text...',
  },
};

/**
 * Input with label.
 */
export const WithLabel: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email">Email</Label>
      <Input type="email" id="email" placeholder="Enter your email" />
    </div>
  ),
};

/**
 * Disabled input state.
 */
export const Disabled: Story = {
  args: {
    disabled: true,
    placeholder: 'Disabled input',
  },
};

/**
 * Required input with label.
 */
export const Required: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="username">
        Username <span className="text-destructive">*</span>
      </Label>
      <Input type="text" id="username" required />
    </div>
  ),
};

/**
 * Input with icon.
 */
export const WithIcon: Story = {
  render: () => (
    <div className="relative w-full max-w-sm">
      <Mail className="text-muted-foreground absolute left-3 top-2.5 h-4 w-4" />
      <Input type="email" placeholder="Email" className="pl-10" />
    </div>
  ),
};

/**
 * Search input with icon.
 */
export const SearchInput: Story = {
  render: () => (
    <div className="relative w-full max-w-sm">
      <SearchIcon className="text-muted-foreground absolute left-3 top-2.5 h-4 w-4" />
      <Input type="search" placeholder="Search..." className="pl-10" />
    </div>
  ),
};

/**
 * File input.
 */
export const File: Story = {
  args: {
    type: 'file',
    className: 'cursor-pointer',
  },
};

/**
 * Different input types.
 */
export const Types: Story = {
  render: () => (
    <div className="grid w-full max-w-sm gap-4">
      <div className="grid gap-1.5">
        <Label htmlFor="text">Text</Label>
        <Input id="text" type="text" placeholder="Text" />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" placeholder="Email" />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="password">Password</Label>
        <Input id="password" type="password" placeholder="Password" />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="number">Number</Label>
        <Input id="number" type="number" placeholder="Number" />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="tel">Tel</Label>
        <Input id="tel" type="tel" placeholder="Tel" />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="url">URL</Label>
        <Input id="url" type="url" placeholder="URL" />
      </div>
    </div>
  ),
};

/**
 * Input with validation states.
 */
export const ValidationStates: Story = {
  render: () => (
    <div className="grid w-full max-w-sm gap-4">
      <div className="grid gap-1.5">
        <Label htmlFor="valid">Valid</Label>
        <Input
          id="valid"
          type="text"
          value="Valid input"
          className="border-green-500 focus-visible:ring-green-500"
        />
      </div>
      <div className="grid gap-1.5">
        <Label htmlFor="invalid">Invalid</Label>
        <Input
          id="invalid"
          type="text"
          value="Invalid input"
          className="border-red-500 focus-visible:ring-red-500"
        />
      </div>
    </div>
  ),
};
