import type { <PERSON>a, StoryObj } from '@storybook/nextjs';
import { Bold, Italic, Underline } from 'lucide-react';

import { Toggle } from '@kit/ui/toggle';

const meta = {
  title: 'Atoms/Toggle',
  component: Toggle,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'outline'],
      description: 'The visual style of the toggle',
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg'],
      description: 'The size of the toggle',
    },
    pressed: {
      control: 'boolean',
      description: 'The controlled pressed state of the toggle',
    },
    defaultPressed: {
      control: 'boolean',
      description: 'The default pressed state when initially rendered',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the toggle is disabled',
    },
    asChild: {
      control: 'boolean',
      description: 'Whether to render as a child element',
    },
  },
} satisfies Meta<typeof Toggle>;

export default meta;
type Story = StoryObj<typeof Toggle>;

export const Default: Story = {
  args: {
    'aria-label': 'Toggle bold',
    children: <Bold className="h-4 w-4" />,
  },
};

export const WithText: Story = {
  args: {
    'aria-label': 'Toggle italic',
    children: (
      <>
        <Italic className="h-4 w-4" />
        <span>Italic</span>
      </>
    ),
  },
};

export const Outline: Story = {
  args: {
    'aria-label': 'Toggle underline',
    variant: 'outline',
    children: <Underline className="h-4 w-4" />,
  },
};

export const Small: Story = {
  args: {
    'aria-label': 'Toggle bold',
    size: 'sm',
    children: <Bold className="h-4 w-4" />,
  },
};

export const Large: Story = {
  args: {
    'aria-label': 'Toggle bold',
    size: 'lg',
    children: <Bold className="h-4 w-4" />,
  },
};

export const Pressed: Story = {
  args: {
    'aria-label': 'Toggle bold',
    pressed: true,
    children: <Bold className="h-4 w-4" />,
  },
};

export const Disabled: Story = {
  args: {
    'aria-label': 'Toggle bold',
    disabled: true,
    children: <Bold className="h-4 w-4" />,
  },
};

export const DisabledPressed: Story = {
  args: {
    'aria-label': 'Toggle bold',
    disabled: true,
    pressed: true,
    children: <Bold className="h-4 w-4" />,
  },
};

export const WithAriaLabel: Story = {
  args: {
    'aria-label': 'Toggle bold text',
    children: <Bold className="h-4 w-4" />,
  },
};

export const WithCustomStyling: Story = {
  args: {
    'aria-label': 'Toggle bold',
    className:
      'data-[state=on]:bg-primary data-[state=on]:text-primary-foreground',
    children: <Bold className="h-4 w-4" />,
  },
};

export const WithTooltip: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Toggle aria-label="Toggle bold">
        <Bold className="h-4 w-4" />
      </Toggle>
      <span className="text-muted-foreground text-sm">
        Press to toggle bold formatting
      </span>
    </div>
  ),
};
