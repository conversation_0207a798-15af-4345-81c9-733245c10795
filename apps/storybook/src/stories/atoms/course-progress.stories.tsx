import type { Meta, StoryObj } from '@storybook/nextjs';

import { CourseProgress } from '@kit/ui/dojo/atoms/course-progress';

// Helper function to create valid progress values
function createProgress(n: number) {
  return Math.min(100, Math.max(0, n)) as number & {
    __brand: 'ProgressPercentage';
  };
}

const meta = {
  title: 'Course/Atoms/CourseProgress',
  component: CourseProgress,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof CourseProgress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    progress: createProgress(45),
    remainingTime: '2h 30m',
  },
};

export const Complete: Story = {
  args: {
    progress: createProgress(100),
    remainingTime: '0m',
  },
};

export const JustStarted: Story = {
  args: {
    progress: createProgress(0),
    remainingTime: '4h',
  },
};
