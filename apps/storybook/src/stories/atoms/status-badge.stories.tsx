import type { Meta, StoryObj } from '@storybook/nextjs';

import { StatusBadge } from '@kit/ui/dojo/atoms/status-badge';

const meta = {
  title: 'Atoms/StatusBadge',
  component: StatusBadge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof StatusBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Draft: Story = {
  args: {
    status: 'draft',
    studentStatus: 'not_enrolled',
  },
};

export const Published: Story = {
  args: {
    status: 'published',
    studentStatus: 'not_enrolled',
  },
};

export const Archived: Story = {
  args: {
    status: 'archived',
    studentStatus: 'not_enrolled',
  },
};

export const Enrolled: Story = {
  args: {
    status: 'published',
    studentStatus: 'enrolled',
  },
};

export const Completed: Story = {
  args: {
    status: 'published',
    studentStatus: 'completed',
  },
};

export const WithOverlay: Story = {
  args: {
    status: 'published',
    studentStatus: 'not_enrolled',
    overlay: true,
  },
};
