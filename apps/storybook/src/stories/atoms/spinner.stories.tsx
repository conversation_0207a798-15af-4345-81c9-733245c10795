import type { <PERSON>a, StoryObj } from '@storybook/nextjs';

import { Spinner } from '@kit/ui/spinner';

const meta = {
  title: 'Atoms/Spinner',
  component: Spinner,
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the spinner',
    },
  },
} satisfies Meta<typeof Spinner>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Small: Story = {
  args: {
    className: 'h-4 w-4',
  },
};

export const Large: Story = {
  args: {
    className: 'h-16 w-16',
  },
};

export const CustomColor: Story = {
  args: {
    className: 'text-blue-500 fill-blue-500/30',
  },
};

export const SlowAnimation: Story = {
  args: {
    className: 'animate-[spin_3s_linear_infinite]',
  },
};

export const FastAnimation: Story = {
  args: {
    className: 'animate-[spin_0.5s_linear_infinite]',
  },
};

export const WithBackground: Story = {
  render: () => (
    <div className="bg-primary/5 flex items-center justify-center rounded-lg p-4">
      <Spinner />
    </div>
  ),
};

export const MultipleSpinners: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Spinner className="h-4 w-4" />
      <Spinner />
      <Spinner className="h-12 w-12" />
      <Spinner className="h-16 w-16" />
    </div>
  ),
};

export const CustomStyles: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Spinner className="text-primary" />
      <Spinner className="text-secondary" />
      <Spinner className="text-destructive" />
      <Spinner className="text-muted-foreground" />
    </div>
  ),
};
