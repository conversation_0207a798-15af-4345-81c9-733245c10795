import * as React from 'react';

import type { Meta, StoryObj } from '@storybook/nextjs';

import { Checkbox } from '@kit/ui/checkbox';
import { Label } from '@kit/ui/label';

// Define CheckedState type explicitly to avoid type errors
type CheckedState = boolean | 'indeterminate';

/**
 * The Checkbox component is used for binary choices and multiple selections.
 * It's built on top of Radix UI Checkbox and styled with Tailwind CSS.
 */
const meta = {
  title: 'Atoms/Checkbox',
  component: Checkbox,
  tags: ['autodocs'],
  argTypes: {
    checked: {
      control: 'boolean',
      description: 'The controlled checked state of the checkbox',
    },
    defaultChecked: {
      control: 'boolean',
      description: 'The default checked state when initially rendered',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the checkbox is disabled',
    },
    required: {
      control: 'boolean',
      description: 'Whether the checkbox is required',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply to the checkbox',
    },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default checkbox with label.
 */
export const Default: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Checkbox id="terms" />
      <Label htmlFor="terms">Accept terms and conditions</Label>
    </div>
  ),
};

/**
 * Checkbox in checked state.
 */
export const Checked: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Checkbox id="checked" defaultChecked />
      <Label htmlFor="checked">Checked by default</Label>
    </div>
  ),
};

/**
 * Disabled checkbox.
 */
export const Disabled: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center space-x-2">
        <Checkbox id="disabled" disabled />
        <Label htmlFor="disabled" className="text-muted-foreground">
          Disabled
        </Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="disabled-checked" disabled defaultChecked />
        <Label htmlFor="disabled-checked" className="text-muted-foreground">
          Disabled checked
        </Label>
      </div>
    </div>
  ),
};

/**
 * Required checkbox with error state.
 */
export const Required: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Checkbox id="required" required />
      <Label htmlFor="required" className="font-medium">
        I agree to the terms*
      </Label>
    </div>
  ),
};

/**
 * Checkbox group example.
 */
export const Group: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center space-x-2">
        <Checkbox id="option1" />
        <Label htmlFor="option1">Option 1</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="option2" />
        <Label htmlFor="option2">Option 2</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox id="option3" />
        <Label htmlFor="option3">Option 3</Label>
      </div>
    </div>
  ),
};

/**
 * Indeterminate state example.
 */
export const Indeterminate: Story = {
  render: () => {
    const [checked, setChecked] = React.useState<[boolean, boolean]>([
      true,
      false,
    ]);

    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="parent"
            checked={checked.every(Boolean)}
            ref={(button: HTMLButtonElement | null) => {
              if (button) {
                (
                  button as unknown as { indeterminate: boolean }
                ).indeterminate =
                  checked.some(Boolean) && !checked.every(Boolean);
              }
            }}
            onCheckedChange={(value: CheckedState) => {
              setChecked([value === true, value === true]);
            }}
          />
          <Label htmlFor="parent">Select all</Label>
        </div>
        <div className="ml-6 flex flex-col gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="child1"
              checked={checked[0]}
              onCheckedChange={(value: CheckedState) => {
                setChecked([value === true, checked[1]]);
              }}
            />
            <Label htmlFor="child1">Child 1</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="child2"
              checked={checked[1]}
              onCheckedChange={(value: CheckedState) => {
                setChecked([checked[0], value === true]);
              }}
            />
            <Label htmlFor="child2">Child 2</Label>
          </div>
        </div>
      </div>
    );
  },
};
