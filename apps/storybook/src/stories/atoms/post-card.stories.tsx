import type { Meta, StoryObj } from '@storybook/nextjs';

import { PostCard } from '@kit/ui/dojo/atoms/post-card';

const meta = {
  title: 'Atoms/PostCard',
  component: PostCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PostCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This is a default post card content',
    header: 'Post Header',
    footer: 'Post Footer',
  },
};

export const Pinned: Story = {
  args: {
    variant: 'pinned',
    children: 'This is a pinned post card content',
    header: 'Pinned Post Header',
    footer: 'Post Footer',
  },
};

export const Draft: Story = {
  args: {
    variant: 'draft',
    children: 'This is a draft post card content',
    header: 'Draft Post Header',
    footer: 'Post Footer',
  },
};

export const ContentOnly: Story = {
  args: {
    children: 'This is a post card with content only',
  },
};

export const WithCustomClasses: Story = {
  args: {
    children: 'This post card has custom classes',
    className: 'bg-purple-100 dark:bg-purple-900',
  },
};
