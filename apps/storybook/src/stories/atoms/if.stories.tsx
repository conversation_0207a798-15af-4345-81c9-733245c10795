import type { Meta, StoryObj } from '@storybook/nextjs';

import { If } from '@kit/ui/if';

const meta = {
  title: 'Atoms/If',
  component: If,
  tags: ['autodocs'],
  argTypes: {
    condition: {
      control: 'boolean',
      description: 'The condition to evaluate',
    },
    children: {
      control: 'text',
      description: 'Content to render when condition is truthy',
    },
    fallback: {
      control: 'text',
      description: 'Content to render when condition is falsy',
    },
  },
} satisfies Meta<typeof If>;

export default meta;
type Story = StoryObj<typeof If>;

export const Default: Story = {
  args: {
    condition: true,
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
  },
};

export const WithFallback: Story = {
  args: {
    condition: false,
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
    fallback: (
      <div className="bg-muted text-muted-foreground p-4">Fallback content</div>
    ),
  },
};

export const WithFunction: Story = {
  args: {
    condition: 'Hello World',
    children: (value: unknown) => (
      <div className="bg-primary text-primary-foreground p-4">
        Value is: {String(value)}
      </div>
    ),
  },
};

export const WithNullCondition: Story = {
  args: {
    condition: null,
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
    fallback: (
      <div className="bg-muted text-muted-foreground p-4">Fallback content</div>
    ),
  },
};

export const WithUndefinedCondition: Story = {
  args: {
    condition: undefined,
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
    fallback: (
      <div className="bg-muted text-muted-foreground p-4">Fallback content</div>
    ),
  },
};

export const WithEmptyString: Story = {
  args: {
    condition: '',
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
    fallback: (
      <div className="bg-muted text-muted-foreground p-4">Fallback content</div>
    ),
  },
};

export const WithZero: Story = {
  args: {
    condition: 0,
    children: (
      <div className="bg-primary text-primary-foreground p-4">
        Content when condition is true
      </div>
    ),
    fallback: (
      <div className="bg-muted text-muted-foreground p-4">Fallback content</div>
    ),
  },
};

export const ComplexExample: Story = {
  render: () => (
    <div className="space-y-4">
      <If condition={true}>
        <div className="bg-primary text-primary-foreground p-4">
          This content is always shown
        </div>
      </If>

      <If
        condition={false}
        fallback={<div className="bg-muted p-4">Fallback content</div>}
      >
        <div className="bg-primary text-primary-foreground p-4">
          This content is never shown
        </div>
      </If>

      <If condition="Dynamic Value">
        {(value: unknown) => (
          <div className="bg-primary text-primary-foreground p-4">
            Using dynamic value: {String(value)}
          </div>
        )}
      </If>
    </div>
  ),
};
