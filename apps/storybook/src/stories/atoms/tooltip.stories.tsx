import type { Meta, StoryObj } from '@storybook/nextjs';
import { Info } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

const meta = {
  title: 'Atoms/Tooltip',
  component: Tooltip,
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <TooltipProvider>
        <div className="flex h-[200px] w-full items-center justify-center">
          <Story />
        </div>
      </TooltipProvider>
    ),
  ],
} satisfies Meta<typeof Tooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline" size="icon">
          <Info className="h-4 w-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Helpful information</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithText: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Hover me</Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Tooltip content</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const TopPosition: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Top tooltip</Button>
      </TooltipTrigger>
      <TooltipContent side="top">
        <p>Appears above the trigger</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const RightPosition: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Right tooltip</Button>
      </TooltipTrigger>
      <TooltipContent side="right">
        <p>Appears to the right</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const BottomPosition: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Bottom tooltip</Button>
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <p>Appears below the trigger</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const LeftPosition: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Left tooltip</Button>
      </TooltipTrigger>
      <TooltipContent side="left">
        <p>Appears to the left</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithDelay: Story = {
  render: () => (
    <TooltipProvider delayDuration={1000}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="outline">Delayed tooltip</Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Appears after 1 second</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ),
};

export const WithAlignment: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Aligned tooltip</Button>
      </TooltipTrigger>
      <TooltipContent align="start" side="top">
        <p>Aligned to the start</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Custom styled</Button>
      </TooltipTrigger>
      <TooltipContent className="bg-primary text-primary-foreground">
        <p>Custom background color</p>
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithMultipleLines: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline">Detailed tooltip</Button>
      </TooltipTrigger>
      <TooltipContent>
        <div className="space-y-1">
          <p className="font-medium">Main title</p>
          <p className="text-muted-foreground text-sm">Additional details</p>
        </div>
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithDisabledButton: Story = {
  render: () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="outline" disabled>
          Disabled button
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Still shows tooltip on disabled elements</p>
      </TooltipContent>
    </Tooltip>
  ),
};
