import type { Meta, StoryObj } from '@storybook/nextjs';

import { Textarea } from '@kit/ui/textarea';

const meta = {
  title: 'Atoms/Textarea',
  component: Textarea,
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the textarea',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the textarea is disabled',
    },
    required: {
      control: 'boolean',
      description: 'Whether the textarea is required',
    },
    rows: {
      control: 'number',
      description: 'Number of visible text lines',
    },
    maxLength: {
      control: 'number',
      description: 'Maximum number of characters allowed',
    },
    readOnly: {
      control: 'boolean',
      description: 'Whether the textarea is read-only',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Textarea placeholder="Type your message here." className="w-[300px]" />
  ),
};

export const WithRows: Story = {
  render: () => (
    <Textarea
      placeholder="Type your message here."
      rows={5}
      className="w-[300px]"
    />
  ),
};

export const Disabled: Story = {
  render: () => (
    <Textarea
      placeholder="This textarea is disabled"
      disabled
      className="w-[300px]"
    />
  ),
};

export const ReadOnly: Story = {
  render: () => (
    <Textarea
      value="This is read-only content that cannot be modified."
      readOnly
      className="w-[300px]"
    />
  ),
};

export const WithMaxLength: Story = {
  render: () => (
    <Textarea
      placeholder="Maximum 100 characters allowed"
      maxLength={100}
      className="w-[300px]"
    />
  ),
};

export const Required: Story = {
  render: () => (
    <Textarea
      placeholder="This field is required"
      required
      className="w-[300px]"
    />
  ),
};

export const WithLabel: Story = {
  render: () => (
    <div className="space-y-2">
      <label
        htmlFor="message"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        Your message
      </label>
      <Textarea
        id="message"
        placeholder="Type your message here."
        className="w-[300px]"
      />
    </div>
  ),
};

export const WithError: Story = {
  render: () => (
    <div className="space-y-2">
      <Textarea
        placeholder="Type your message here."
        className="w-[300px] border-red-500 focus-visible:ring-red-500"
        aria-invalid="true"
        aria-errormessage="message-error"
      />
      <p id="message-error" className="text-sm font-medium text-red-500">
        Please enter a valid message
      </p>
    </div>
  ),
};

export const WithCharacterCount: Story = {
  render: () => {
    const maxLength = 100;
    return (
      <div className="space-y-2">
        <Textarea
          placeholder="Type your message here."
          maxLength={maxLength}
          className="w-[300px]"
          onChange={(e) => {
            const remaining = maxLength - e.target.value.length;
            const counter = document.getElementById('char-count');
            if (counter) {
              counter.textContent = `${remaining} characters remaining`;
            }
          }}
        />
        <p id="char-count" className="text-muted-foreground text-sm">
          {maxLength} characters remaining
        </p>
      </div>
    );
  },
};

export const WithCustomStyling: Story = {
  render: () => (
    <Textarea
      placeholder="Custom styled textarea"
      className="border-primary bg-primary/5 placeholder:text-primary/50 focus-visible:ring-primary w-[300px] border-2"
    />
  ),
};

export const Resizable: Story = {
  render: () => (
    <Textarea
      placeholder="This textarea can be resized"
      className="w-[300px] resize-y"
    />
  ),
};
